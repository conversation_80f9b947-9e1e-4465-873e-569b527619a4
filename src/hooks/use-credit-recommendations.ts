import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useDispatch } from 'react-redux'
import { openPurchaseModal } from '@/store/slices/creditSlice'
import type { AppDispatch } from '@/lib/store'

interface CreditRecommendation {
  packageId: string
  package: any
  reason: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  savings?: number
  matchScore: number
  benefits: string[]
  context: string
  currentBalance: number
  valuePerCredit: number
  estimatedUsageDays: number
  popularityRank: number
}

interface UseRecommendationsOptions {
  requiredCredits?: number
  context?: 'chapter_access' | 'novel_unlock' | 'subscription_renewal' | 'general'
  metadata?: Record<string, any>
  autoFetch?: boolean
}

interface UseRecommendationsReturn {
  recommendations: CreditRecommendation[]
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
  trackAction: (packageId: string, action: 'viewed' | 'clicked' | 'purchased' | 'dismissed') => Promise<void>
  openPurchaseWithRecommendation: (packageId: string) => void
}

export function useCreditRecommendations(
  options: UseRecommendationsOptions = {}
): UseRecommendationsReturn {
  const { data: session } = useSession()
  const dispatch = useDispatch<AppDispatch>()
  
  const [recommendations, setRecommendations] = useState<CreditRecommendation[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const {
    requiredCredits,
    context = 'general',
    metadata,
    autoFetch = true
  } = options

  const fetchRecommendations = async () => {
    if (!session?.user) {
      setRecommendations([])
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams()
      
      if (requiredCredits !== undefined) {
        params.append('requiredCredits', requiredCredits.toString())
      }
      
      if (context) {
        params.append('context', context)
      }
      
      if (metadata) {
        params.append('metadata', JSON.stringify(metadata))
      }

      const response = await fetch(`/api/credits/recommendations?${params.toString()}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch recommendations')
      }

      const data = await response.json()
      setRecommendations(data.recommendations || [])

      // Track that recommendations were viewed
      if (data.recommendations?.length > 0) {
        data.recommendations.forEach((rec: CreditRecommendation) => {
          trackAction(rec.packageId, 'viewed')
        })
      }

    } catch (err) {
      console.error('Error fetching recommendations:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
      setRecommendations([])
    } finally {
      setIsLoading(false)
    }
  }

  const trackAction = async (
    packageId: string, 
    action: 'viewed' | 'clicked' | 'purchased' | 'dismissed'
  ) => {
    try {
      await fetch('/api/credits/recommendations/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          packageId,
          action,
          context: context || 'general'
        })
      })
    } catch (error) {
      console.error('Error tracking recommendation action:', error)
      // Don't throw - tracking failures shouldn't break the UI
    }
  }

  const openPurchaseWithRecommendation = (packageId: string) => {
    // Track the click action
    trackAction(packageId, 'clicked')
    
    // Open purchase modal with the recommended package
    dispatch(openPurchaseModal(packageId))
  }

  // Auto-fetch on mount and when dependencies change
  useEffect(() => {
    if (autoFetch) {
      fetchRecommendations()
    }
  }, [session?.user, requiredCredits, context, JSON.stringify(metadata), autoFetch])

  return {
    recommendations,
    isLoading,
    error,
    refetch: fetchRecommendations,
    trackAction,
    openPurchaseWithRecommendation
  }
}

// Specialized hooks for common scenarios
export function useChapterAccessRecommendations(requiredCredits: number) {
  return useCreditRecommendations({
    requiredCredits,
    context: 'chapter_access',
    metadata: { requiredCredits }
  })
}

export function useNovelUnlockRecommendations(novelId: string, totalCredits: number) {
  return useCreditRecommendations({
    requiredCredits: totalCredits,
    context: 'novel_unlock',
    metadata: { novelId, totalCredits }
  })
}

export function useLowBalanceRecommendations(currentBalance: number) {
  return useCreditRecommendations({
    context: 'general',
    metadata: { trigger: 'low_balance', currentBalance },
    autoFetch: currentBalance < 20 // Only auto-fetch when balance is actually low
  })
}

// Hook for getting recommendations with smart caching
export function useSmartRecommendations(options: UseRecommendationsOptions = {}) {
  const [lastFetch, setLastFetch] = useState<number>(0)
  const [cachedRecommendations, setCachedRecommendations] = useState<CreditRecommendation[]>([])
  
  const baseHook = useCreditRecommendations({
    ...options,
    autoFetch: false // We'll control fetching manually
  })

  const shouldRefetch = () => {
    const now = Date.now()
    const fiveMinutes = 5 * 60 * 1000
    return now - lastFetch > fiveMinutes
  }

  const smartRefetch = async () => {
    if (shouldRefetch()) {
      await baseHook.refetch()
      setLastFetch(Date.now())
      setCachedRecommendations(baseHook.recommendations)
    }
  }

  // Auto-fetch on mount if cache is stale
  useEffect(() => {
    if (shouldRefetch()) {
      smartRefetch()
    }
  }, [])

  return {
    ...baseHook,
    recommendations: cachedRecommendations.length > 0 ? cachedRecommendations : baseHook.recommendations,
    refetch: smartRefetch
  }
}
