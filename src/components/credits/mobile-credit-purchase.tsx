"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { useDispatch, useSelector } from "react-redux"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"

import { useToast } from "@/hooks/use-toast"
import { 
  Coins, 
  CreditCard, 
  Check, 
  Zap,
  Plus,
  Smartphone,
  ArrowRight
} from "lucide-react"
import { formatCredits } from "@/lib/credits"
import { loadStripe } from "@stripe/stripe-js"
import { 
  useGetCreditPackagesQuery, 
  usePurchaseCreditsMutation 
} from "@/store/api/creditsApi"
import { 
  selectPurchaseModal,
  closePurchaseModal,
  setSelectedPackage,
  handleSuccessfulPurchase
} from "@/store/slices/creditSlice"
import type { AppDispatch } from "@/lib/store"

interface CreditPackage {
  id: string
  name: string
  description: string
  credits: number
  bonusCredits: number
  price: number
  currency: string
  sortOrder: number
}

interface MobileCreditPurchaseProps {
  trigger?: React.ReactNode
  onSuccess?: () => void
}

export function MobileCreditPurchase({ trigger, onSuccess }: MobileCreditPurchaseProps) {
  const { data: session } = useSession()
  const { toast } = useToast()
  const dispatch = useDispatch<AppDispatch>()
  
  // Redux state
  const { isOpen, selectedPackageId } = useSelector(selectPurchaseModal)
  
  // RTK Query
  const { data: packagesData, isLoading } = useGetCreditPackagesQuery()
  const [purchaseCredits, { isLoading: isPurchasing }] = usePurchaseCreditsMutation()
  
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const packages = packagesData?.packages || []

  const handlePurchase = async (packageId: string) => {
    if (!session) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to purchase credits",
        variant: "destructive"
      })
      return
    }

    dispatch(setSelectedPackage(packageId))

    try {
      const stripe = await loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)
      if (!stripe) throw new Error('Stripe not loaded')

      // Create a test payment method (in production, collect from user)
      const { paymentMethod, error: pmError } = await stripe.createPaymentMethod({
        type: 'card',
        card: {
          number: '****************',
          exp_month: 12,
          exp_year: 2025,
          cvc: '123',
        },
      })

      if (pmError || !paymentMethod) {
        throw new Error(pmError?.message || 'Failed to create payment method')
      }

      // Purchase credits using RTK Query
      const result = await purchaseCredits({
        packageId,
        paymentMethodId: paymentMethod.id,
      }).unwrap()

      if (result.requiresAction && result.clientSecret) {
        const { error: confirmError } = await stripe.confirmCardPayment(result.clientSecret)
        if (confirmError) {
          throw new Error(confirmError.message)
        }
      }

      toast({
        title: "Credits Purchased!",
        description: `Successfully purchased ${formatCredits(result.purchase.totalCredits)}`,
      })

      // Update Redux state
      dispatch(handleSuccessfulPurchase(result.purchase.totalCredits, {
        id: result.purchase.id,
        description: `Purchased ${result.purchase.credits} credits${result.purchase.bonusCredits > 0 ? ` + ${result.purchase.bonusCredits} bonus` : ''} (${result.purchase.package.name})`,
        createdAt: new Date().toISOString(),
      }))

      setIsDialogOpen(false)
      onSuccess?.()

    } catch (error: any) {
      console.error('Purchase error:', error)
      toast({
        title: "Purchase Failed",
        description: error.message || "Failed to purchase credits. Please try again.",
        variant: "destructive",
      })
    } finally {
      dispatch(setSelectedPackage(null))
    }
  }

  const getPopularBadge = (index: number) => {
    if (index === 1) { // Second package (Value Pack)
      return (
        <Badge className="absolute -top-2 -right-2 bg-blue-500 hover:bg-blue-600 text-xs">
          Popular
        </Badge>
      )
    }
    return null
  }

  const getBestValueBadge = (index: number) => {
    if (index === packages.length - 1) { // Last package (Ultimate Pack)
      return (
        <Badge className="absolute -top-2 -left-2 bg-green-500 hover:bg-green-600 text-xs">
          Best Value
        </Badge>
      )
    }
    return null
  }

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button size="sm" className="gap-2">
            <Plus className="h-4 w-4" />
            <span className="hidden sm:inline">Buy Credits</span>
            <Smartphone className="h-4 w-4 sm:hidden" />
          </Button>
        )}
      </DialogTrigger>

      <DialogContent className="max-w-md max-h-[90vh] p-0">
        <div className="flex flex-col h-full">
          <DialogHeader className="p-6 pb-4 border-b">
            <DialogTitle className="flex items-center gap-2">
              <Coins className="h-5 w-5 text-yellow-500" />
              Purchase Credits
            </DialogTitle>
            <DialogDescription>
              Choose a credit package to unlock premium content. Credits never expire.
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 p-6 overflow-y-auto">
            <div className="space-y-4">
              {isLoading ? (
                Array.from({ length: 4 }).map((_, i) => (
                  <Card key={i} className="relative">
                    <CardHeader className="pb-3">
                      <Skeleton className="h-6 w-24" />
                      <Skeleton className="h-4 w-32" />
                    </CardHeader>
                    <CardContent>
                      <Skeleton className="h-8 w-16 mb-2" />
                      <Skeleton className="h-4 w-20 mb-4" />
                      <Skeleton className="h-12 w-full" />
                    </CardContent>
                  </Card>
                ))
              ) : (
                packages.map((pkg, index) => (
                  <Card 
                    key={pkg.id} 
                    className={`relative transition-all ${
                      selectedPackageId === pkg.id ? 'ring-2 ring-blue-500 shadow-lg' : 'hover:shadow-md'
                    }`}
                  >
                    {getPopularBadge(index)}
                    {getBestValueBadge(index)}
                    
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg flex items-center justify-between">
                        {pkg.name}
                        <div className="text-right">
                          <div className="text-xl font-bold">
                            ${pkg.price}
                          </div>
                        </div>
                      </CardTitle>
                      <CardDescription className="text-sm">
                        {pkg.description}
                      </CardDescription>
                    </CardHeader>
                    
                    <CardContent className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-lg font-semibold">
                            {formatCredits(pkg.credits)}
                          </div>
                          {pkg.bonusCredits > 0 && (
                            <div className="text-sm text-green-600 font-medium flex items-center gap-1">
                              <Zap className="h-3 w-3" />
                              +{formatCredits(pkg.bonusCredits)} bonus
                            </div>
                          )}
                        </div>
                        
                        {pkg.bonusCredits > 0 && (
                          <Badge variant="outline" className="text-green-600 border-green-600">
                            {Math.round((pkg.bonusCredits / pkg.credits) * 100)}% bonus
                          </Badge>
                        )}
                      </div>

                      <Button
                        onClick={() => handlePurchase(pkg.id)}
                        disabled={isPurchasing}
                        className="w-full h-12 text-base"
                        variant={selectedPackageId === pkg.id ? "default" : "outline"}
                      >
                        {isPurchasing && selectedPackageId === pkg.id ? (
                          <>
                            <CreditCard className="h-5 w-5 mr-2 animate-pulse" />
                            Processing...
                          </>
                        ) : (
                          <>
                            <CreditCard className="h-5 w-5 mr-2" />
                            Purchase
                            <ArrowRight className="h-4 w-4 ml-2" />
                          </>
                        )}
                      </Button>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </div>

          <div className="p-6 pt-4 border-t bg-gray-50">
            <div className="flex items-start gap-3">
              <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-gray-600">
                <p className="font-medium mb-1">What you get:</p>
                <ul className="space-y-1 text-xs">
                  <li>• Instant access to premium content</li>
                  <li>• Credits never expire</li>
                  <li>• Support your favorite authors</li>
                  <li>• Secure payment processing</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
