"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useDispatch, useSelector } from "react-redux"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useToast } from "@/hooks/use-toast"
import {
  Coins,
  Plus,
  RefreshCw,
  TrendingUp,
  AlertTriangle,
  Zap,
  Gift,
  Target
} from "lucide-react"
import { formatCredits, creditsToUSD } from "@/lib/credits"
import { CreditPurchaseModal } from "./credit-purchase-modal"
import { useGetCreditBalanceQuery, useGetCreditPackagesQuery } from "@/store/api/creditsApi"
import {
  selectCreditBalance,
  selectCreditLoading,
  selectShouldShowLowBalanceWarning,
  setCreditBalance,
  openPurchaseModal,
  checkAndShowLowBalanceWarning
} from "@/store/slices/creditSlice"
import type { AppDispatch } from "@/lib/store"

interface CreditBalanceProps {
  showPurchaseButton?: boolean
  className?: string
  showRecommendations?: boolean
  compact?: boolean
  requiredCredits?: number // For contextual recommendations
}

export function CreditBalance({
  showPurchaseButton = true,
  className,
  showRecommendations = true,
  compact = false,
  requiredCredits
}: CreditBalanceProps) {
  const { data: session } = useSession()
  const { toast } = useToast()
  const dispatch = useDispatch<AppDispatch>()

  // Redux state
  const balance = useSelector(selectCreditBalance)
  const isLoading = useSelector(selectCreditLoading)
  const shouldShowLowBalanceWarning = useSelector(selectShouldShowLowBalanceWarning)

  // RTK Query
  const {
    data: balanceData,
    isLoading: isQueryLoading,
    refetch
  } = useGetCreditBalanceQuery(undefined, {
    skip: !session?.user,
    pollingInterval: 30000, // Poll every 30 seconds
  })

  const { data: packagesData } = useGetCreditPackagesQuery(undefined, {
    skip: !session?.user || !showRecommendations,
  })

  const [isRefreshing, setIsRefreshing] = useState(false)
  const packages = packagesData?.packages || []

  // Update Redux state when balance data changes
  useEffect(() => {
    if (balanceData?.balance !== undefined) {
      dispatch(setCreditBalance(balanceData.balance))
    }
  }, [balanceData, dispatch])

  // Check for low balance warning
  useEffect(() => {
    if (balance > 0) {
      dispatch(checkAndShowLowBalanceWarning())
    }
  }, [balance, dispatch])

  const handleRefresh = async () => {
    setIsRefreshing(true)
    try {
      await refetch()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to refresh credit balance",
        variant: "destructive"
      })
    } finally {
      setIsRefreshing(false)
    }
  }

  // Generate contextual recommendations
  const getRecommendation = () => {
    if (!showRecommendations || packages.length === 0) return null

    // If user needs specific credits for content
    if (requiredCredits && balance < requiredCredits) {
      const shortfall = requiredCredits - balance
      const recommendedPackage = packages.find(pkg =>
        (pkg.credits + pkg.bonusCredits) >= shortfall
      )

      if (recommendedPackage) {
        return {
          type: 'insufficient',
          message: `You need ${formatCredits(shortfall)} more to unlock this content`,
          package: recommendedPackage,
          urgent: true
        }
      }
    }

    // Low balance recommendations
    if (balance < 10) {
      const starterPackage = packages.find(pkg => pkg.credits <= 100) || packages[0]
      return {
        type: 'low_balance',
        message: 'Your balance is running low',
        package: starterPackage,
        urgent: true
      }
    }

    // Value recommendations for regular users
    if (balance < 50) {
      const valuePackage = packages.find(pkg => pkg.bonusCredits > 0) || packages[1]
      return {
        type: 'value',
        message: 'Get bonus credits with value packs',
        package: valuePackage,
        urgent: false
      }
    }

    return null
  }

  const recommendation = getRecommendation()

  const handlePurchaseClick = (packageId?: string) => {
    dispatch(openPurchaseModal(packageId || null))
  }

  if (!session) {
    return (
      <Card className={className}>
        <CardContent className={compact ? "pt-4" : "pt-6"}>
          <div className="flex items-center justify-center text-muted-foreground">
            <Coins className="h-5 w-5 mr-2" />
            Sign in to view credits
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card className={className}>
        {!compact && (
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Coins className="h-5 w-5 text-yellow-500" />
              Credit Balance
            </CardTitle>
            <CardDescription>
              Use credits to unlock premium content
            </CardDescription>
          </CardHeader>
        )}
        <CardContent className={compact ? "pt-4" : ""}>
          {compact && (
            <div className="flex items-center gap-2 mb-3">
              <Coins className="h-4 w-4 text-yellow-500" />
              <span className="font-medium text-sm">Credits</span>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {isLoading || isQueryLoading ? (
                <Skeleton className="h-8 w-24" />
              ) : (
                <div className="flex items-center gap-2">
                  <Badge
                    variant={balance < 10 ? "destructive" : balance < 50 ? "secondary" : "default"}
                    className={`${compact ? 'text-base' : 'text-lg'} font-bold px-3 py-1`}
                  >
                    {formatCredits(balance)}
                  </Badge>
                  {!compact && (
                    <span className="text-sm text-muted-foreground">
                      (${creditsToUSD(balance).toFixed(2)})
                    </span>
                  )}
                </div>
              )}

              <Button
                variant="ghost"
                size="sm"
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="h-8 w-8 p-0"
              >
                <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              </Button>
            </div>

            {showPurchaseButton && (
              <div className="flex items-center gap-2">
                {recommendation && recommendation.urgent && (
                  <Button
                    onClick={() => handlePurchaseClick(recommendation.package?.id)}
                    size="sm"
                    variant="default"
                    className="gap-2"
                  >
                    <Zap className="h-4 w-4" />
                    {compact ? "Top Up" : "Quick Top-up"}
                  </Button>
                )}
                <Button
                  onClick={() => handlePurchaseClick()}
                  size="sm"
                  variant={recommendation?.urgent ? "outline" : "default"}
                  className="gap-2"
                >
                  <Plus className="h-4 w-4" />
                  {compact ? "Buy" : "Buy Credits"}
                </Button>
              </div>
            )}
          </div>

          {/* Contextual Recommendations */}
          {recommendation && !compact && (
            <Alert className={`mt-3 ${recommendation.urgent ? 'border-orange-200 bg-orange-50' : 'border-blue-200 bg-blue-50'}`}>
              <div className="flex items-start gap-2">
                {recommendation.urgent ? (
                  <AlertTriangle className="h-4 w-4 text-orange-600 mt-0.5" />
                ) : (
                  <TrendingUp className="h-4 w-4 text-blue-600 mt-0.5" />
                )}
                <div className="flex-1">
                  <AlertDescription className={recommendation.urgent ? 'text-orange-800' : 'text-blue-800'}>
                    <div className="font-medium mb-1">{recommendation.message}</div>
                    {recommendation.package && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm">
                          Recommended: {recommendation.package.name} - {formatCredits(recommendation.package.credits + recommendation.package.bonusCredits)} for ${recommendation.package.price}
                          {recommendation.package.bonusCredits > 0 && (
                            <span className="text-green-600 ml-1">
                              (+{formatCredits(recommendation.package.bonusCredits)} bonus)
                            </span>
                          )}
                        </span>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handlePurchaseClick(recommendation.package?.id)}
                          className="ml-2"
                        >
                          <Gift className="h-3 w-3 mr-1" />
                          Get Now
                        </Button>
                      </div>
                    )}
                  </AlertDescription>
                </div>
              </div>
            </Alert>
          )}

          {/* Legacy low balance warning */}
          {shouldShowLowBalanceWarning && !recommendation && (
            <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-sm text-yellow-800">
                Your credit balance is low. Consider purchasing more credits to continue enjoying premium content.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      <CreditPurchaseModal />
    </>
  )
}
