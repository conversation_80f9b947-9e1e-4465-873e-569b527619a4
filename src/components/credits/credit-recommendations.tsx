"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Lightbulb, 
  Star, 
  TrendingUp, 
  Gift, 
  Zap, 
  Target,
  X,
  CheckCircle,
  AlertTriangle
} from "lucide-react"
import { formatCredits, creditsToUSD } from "@/lib/credits"
import { useCreditRecommendations } from "@/hooks/use-credit-recommendations"

interface CreditRecommendationsProps {
  requiredCredits?: number
  context?: 'chapter_access' | 'novel_unlock' | 'subscription_renewal' | 'general'
  metadata?: Record<string, any>
  className?: string
  compact?: boolean
  maxRecommendations?: number
  showDismiss?: boolean
}

export function CreditRecommendations({
  requiredCredits,
  context = 'general',
  metadata,
  className,
  compact = false,
  maxRecommendations = 3,
  showDismiss = true
}: CreditRecommendationsProps) {
  const [dismissedRecommendations, setDismissedRecommendations] = useState<Set<string>>(new Set())
  
  const {
    recommendations,
    isLoading,
    error,
    trackAction,
    openPurchaseWithRecommendation
  } = useCreditRecommendations({
    requiredCredits,
    context,
    metadata
  })

  const handleDismiss = async (packageId: string) => {
    setDismissedRecommendations(prev => new Set([...prev, packageId]))
    await trackAction(packageId, 'dismissed')
  }

  const handlePurchaseClick = (packageId: string) => {
    openPurchaseWithRecommendation(packageId)
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'high':
        return <Star className="h-4 w-4 text-orange-500" />
      case 'medium':
        return <TrendingUp className="h-4 w-4 text-blue-500" />
      default:
        return <Lightbulb className="h-4 w-4 text-gray-500" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'border-red-200 bg-red-50'
      case 'high':
        return 'border-orange-200 bg-orange-50'
      case 'medium':
        return 'border-blue-200 bg-blue-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  // Filter out dismissed recommendations
  const visibleRecommendations = recommendations
    .filter(rec => !dismissedRecommendations.has(rec.packageId))
    .slice(0, maxRecommendations)

  if (isLoading) {
    return (
      <div className={className}>
        {Array.from({ length: 2 }).map((_, i) => (
          <Card key={i} className="mb-3">
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4" />
                <Skeleton className="h-4 w-32" />
              </div>
            </CardHeader>
            <CardContent>
              <Skeleton className="h-3 w-full mb-2" />
              <Skeleton className="h-3 w-3/4 mb-3" />
              <Skeleton className="h-8 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <Alert className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Unable to load recommendations. Please try again later.
        </AlertDescription>
      </Alert>
    )
  }

  if (visibleRecommendations.length === 0) {
    return null
  }

  if (compact) {
    return (
      <div className={`space-y-2 ${className}`}>
        {visibleRecommendations.map((recommendation) => (
          <div
            key={recommendation.packageId}
            className={`p-3 rounded-lg border ${getPriorityColor(recommendation.priority)}`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {getPriorityIcon(recommendation.priority)}
                <span className="text-sm font-medium">{recommendation.package.name}</span>
                <Badge variant="outline" className="text-xs">
                  {formatCredits(recommendation.package.credits + recommendation.package.bonusCredits)}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  onClick={() => handlePurchaseClick(recommendation.packageId)}
                  className="h-7 px-3 text-xs"
                >
                  ${recommendation.package.price}
                </Button>
                {showDismiss && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDismiss(recommendation.packageId)}
                    className="h-7 w-7 p-0"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
              </div>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {recommendation.reason}
            </p>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center gap-2">
        <Lightbulb className="h-5 w-5 text-blue-500" />
        <h3 className="font-semibold">Recommended for You</h3>
      </div>

      {visibleRecommendations.map((recommendation) => (
        <Card 
          key={recommendation.packageId}
          className={`border-2 ${getPriorityColor(recommendation.priority)} hover:shadow-md transition-shadow`}
        >
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {getPriorityIcon(recommendation.priority)}
                <CardTitle className="text-lg">{recommendation.package.name}</CardTitle>
                <Badge 
                  variant={recommendation.priority === 'urgent' ? 'destructive' : 'secondary'}
                  className="text-xs"
                >
                  {recommendation.priority}
                </Badge>
              </div>
              {showDismiss && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDismiss(recommendation.packageId)}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
            <CardDescription className="font-medium text-base">
              {recommendation.reason}
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-4">
            {/* Package Details */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Credits</p>
                <p className="font-bold text-lg">
                  {formatCredits(recommendation.package.credits)}
                  {recommendation.package.bonusCredits > 0 && (
                    <span className="text-green-600 ml-1">
                      +{formatCredits(recommendation.package.bonusCredits)}
                    </span>
                  )}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Price</p>
                <p className="font-bold text-lg">${recommendation.package.price}</p>
                <p className="text-xs text-muted-foreground">
                  ${recommendation.valuePerCredit.toFixed(3)} per credit
                </p>
              </div>
            </div>

            {/* Benefits */}
            {recommendation.benefits.length > 0 && (
              <div>
                <p className="text-sm font-medium mb-2">Benefits:</p>
                <ul className="space-y-1">
                  {recommendation.benefits.map((benefit, index) => (
                    <li key={index} className="flex items-center gap-2 text-sm">
                      <CheckCircle className="h-3 w-3 text-green-500" />
                      {benefit}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Savings */}
            {recommendation.savings && recommendation.savings > 0 && (
              <div className="flex items-center gap-2 p-2 bg-green-50 rounded-lg border border-green-200">
                <Gift className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">
                  Save ${recommendation.savings.toFixed(2)} with bonus credits!
                </span>
              </div>
            )}

            {/* Action Button */}
            <Button
              onClick={() => handlePurchaseClick(recommendation.packageId)}
              className="w-full"
              size="lg"
            >
              <Zap className="h-4 w-4 mr-2" />
              Get {recommendation.package.name} - ${recommendation.package.price}
            </Button>

            {/* Match Score */}
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>Match Score: {recommendation.matchScore}%</span>
              <span>
                {recommendation.context === 'insufficient_credits' && '🎯 Perfect Match'}
                {recommendation.context === 'low_balance' && '⚡ Quick Top-up'}
                {recommendation.context === 'value_optimization' && '💰 Best Value'}
                {recommendation.context === 'usage_pattern' && '📊 Based on Usage'}
              </span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
