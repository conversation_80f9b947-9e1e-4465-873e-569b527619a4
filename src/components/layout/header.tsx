"use client"

import Link from "next/link"
import { useSession } from "next-auth/react"
import { SignInButton } from "@/components/auth/signin-button"
import { ThemeToggle } from "@/components/common/theme-toggle"
import { CreditBalanceWidget } from "@/components/reader/credit-balance-widget"
import { MobileCreditBalance } from "@/components/credits/mobile-credit-balance"
import { Search, Menu } from "lucide-react"
import { Button } from "@/components/ui/button"

export function Header() {
  const { data: session } = useSession()

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        <div className="mr-4 flex">
          <Link href="/" className="mr-6 flex items-center space-x-2">
            <div className="h-6 w-6 bg-primary rounded-sm flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm">B</span>
            </div>
            <span className="hidden font-bold sm:inline-block">
              Black Blogs
            </span>
          </Link>
          <nav className="hidden md:flex items-center space-x-6 text-sm font-medium">
            <Link
              href="/browse"
              className="transition-colors hover:text-foreground/80 text-foreground/60"
            >
              Browse
            </Link>
            {session?.user?.role === "AUTHOR" && (
              <Link
                href="/dashboard"
                className="transition-colors hover:text-foreground/80 text-foreground/60"
              >
                Dashboard
              </Link>
            )}
            {session && (
              <Link
                href="/library"
                className="transition-colors hover:text-foreground/80 text-foreground/60"
              >
                Library
              </Link>
            )}
          </nav>
        </div>

        <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
          <div className="w-full flex-1 md:w-auto md:flex-none">
            <Button variant="ghost" size="sm" className="h-8 w-8 px-0 md:hidden">
              <Search className="h-4 w-4" />
              <span className="sr-only">Search</span>
            </Button>
          </div>

          <nav className="flex items-center space-x-2">
            {/* Credit Balance - only show for authenticated users who are not authors */}
            {session?.user && session.user.role !== "AUTHOR" && (
              <>
                {/* Desktop Credit Balance */}
                <CreditBalanceWidget
                  compact
                  showPurchaseButton={true}
                  className="hidden sm:flex"
                />
                {/* Mobile Credit Balance */}
                <MobileCreditBalance
                  compact
                  showPurchaseButton={true}
                  className="flex sm:hidden"
                />
              </>
            )}

            <ThemeToggle />
            <SignInButton />

            {/* Mobile menu button */}
            <Button variant="ghost" size="sm" className="h-8 w-8 px-0 md:hidden">
              <Menu className="h-4 w-4" />
              <span className="sr-only">Menu</span>
            </Button>
          </nav>
        </div>
      </div>
    </header>
  )
}