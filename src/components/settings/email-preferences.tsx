"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useToast } from "@/hooks/use-toast"
import { 
  Mail, 
  Bell, 
  CreditCard, 
  TrendingUp, 
  ShoppingCart,
  AlertTriangle,
  CheckCircle,
  Loader2
} from "lucide-react"

interface EmailPreferences {
  emailNotifications: boolean
  lowBalanceWarnings: boolean
  purchaseConfirmations: boolean
  weeklyDigest: boolean
  marketingEmails: boolean
  purchaseReminders: boolean
}

export function EmailPreferences() {
  const { data: session } = useSession()
  const { toast } = useToast()
  
  const [preferences, setPreferences] = useState<EmailPreferences>({
    emailNotifications: true,
    lowBalanceWarnings: true,
    purchaseConfirmations: true,
    weeklyDigest: false,
    marketingEmails: false,
    purchaseReminders: true,
  })
  
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  // Load user preferences
  useEffect(() => {
    if (session?.user) {
      fetchPreferences()
    }
  }, [session])

  const fetchPreferences = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/user/email-preferences')
      
      if (response.ok) {
        const data = await response.json()
        setPreferences(data.preferences)
      } else {
        throw new Error('Failed to load preferences')
      }
    } catch (error) {
      console.error('Error loading email preferences:', error)
      toast({
        title: "Error",
        description: "Failed to load email preferences",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const updatePreference = (key: keyof EmailPreferences, value: boolean) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }))
    setHasChanges(true)
  }

  const savePreferences = async () => {
    try {
      setIsSaving(true)
      
      const response = await fetch('/api/user/email-preferences', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(preferences)
      })

      if (response.ok) {
        setHasChanges(false)
        toast({
          title: "Preferences Updated",
          description: "Your email preferences have been saved successfully",
        })
      } else {
        throw new Error('Failed to save preferences')
      }
    } catch (error) {
      console.error('Error saving email preferences:', error)
      toast({
        title: "Error",
        description: "Failed to save email preferences",
        variant: "destructive"
      })
    } finally {
      setIsSaving(false)
    }
  }

  const resetPreferences = () => {
    fetchPreferences()
    setHasChanges(false)
  }

  if (!session?.user) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <Mail className="h-8 w-8 mx-auto mb-2" />
            <p>Sign in to manage your email preferences</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="h-5 w-5" />
          Email Preferences
        </CardTitle>
        <CardDescription>
          Manage your email notification settings and communication preferences
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="h-4 w-32 bg-gray-200 rounded animate-pulse" />
                  <div className="h-3 w-48 bg-gray-100 rounded animate-pulse" />
                </div>
                <div className="h-6 w-11 bg-gray-200 rounded-full animate-pulse" />
              </div>
            ))}
          </div>
        ) : (
          <>
            {/* Master Email Toggle */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="emailNotifications" className="text-base font-medium">
                    Email Notifications
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Master toggle for all email communications
                  </p>
                </div>
                <Switch
                  id="emailNotifications"
                  checked={preferences.emailNotifications}
                  onCheckedChange={(checked) => updatePreference('emailNotifications', checked)}
                />
              </div>

              {!preferences.emailNotifications && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    You will not receive any email notifications. Important purchase confirmations may still be sent for legal compliance.
                  </AlertDescription>
                </Alert>
              )}
            </div>

            <Separator />

            {/* Individual Email Types */}
            <div className="space-y-4">
              <h4 className="font-medium">Notification Types</h4>
              
              <div className="space-y-4">
                {/* Purchase Confirmations */}
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="purchaseConfirmations" className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4" />
                      Purchase Confirmations
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Receipts and confirmations for credit purchases
                    </p>
                  </div>
                  <Switch
                    id="purchaseConfirmations"
                    checked={preferences.purchaseConfirmations}
                    onCheckedChange={(checked) => updatePreference('purchaseConfirmations', checked)}
                    disabled={!preferences.emailNotifications}
                  />
                </div>

                {/* Low Balance Warnings */}
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="lowBalanceWarnings" className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4" />
                      Low Balance Warnings
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Alerts when your credit balance is running low
                    </p>
                  </div>
                  <Switch
                    id="lowBalanceWarnings"
                    checked={preferences.lowBalanceWarnings}
                    onCheckedChange={(checked) => updatePreference('lowBalanceWarnings', checked)}
                    disabled={!preferences.emailNotifications}
                  />
                </div>

                {/* Purchase Reminders */}
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="purchaseReminders" className="flex items-center gap-2">
                      <ShoppingCart className="h-4 w-4" />
                      Purchase Reminders
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Reminders for incomplete credit purchases
                    </p>
                  </div>
                  <Switch
                    id="purchaseReminders"
                    checked={preferences.purchaseReminders}
                    onCheckedChange={(checked) => updatePreference('purchaseReminders', checked)}
                    disabled={!preferences.emailNotifications}
                  />
                </div>

                {/* Weekly Digest */}
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="weeklyDigest" className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4" />
                      Weekly Reading Digest
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Weekly summary of your reading activity and stats
                    </p>
                  </div>
                  <Switch
                    id="weeklyDigest"
                    checked={preferences.weeklyDigest}
                    onCheckedChange={(checked) => updatePreference('weeklyDigest', checked)}
                    disabled={!preferences.emailNotifications}
                  />
                </div>

                {/* Marketing Emails */}
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="marketingEmails" className="flex items-center gap-2">
                      <Bell className="h-4 w-4" />
                      Marketing & Promotions
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Special offers, new features, and promotional content
                    </p>
                  </div>
                  <Switch
                    id="marketingEmails"
                    checked={preferences.marketingEmails}
                    onCheckedChange={(checked) => updatePreference('marketingEmails', checked)}
                    disabled={!preferences.emailNotifications}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Action Buttons */}
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                {hasChanges ? (
                  <span className="flex items-center gap-1 text-orange-600">
                    <AlertTriangle className="h-3 w-3" />
                    You have unsaved changes
                  </span>
                ) : (
                  <span className="flex items-center gap-1 text-green-600">
                    <CheckCircle className="h-3 w-3" />
                    All changes saved
                  </span>
                )}
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  onClick={resetPreferences}
                  disabled={!hasChanges || isSaving}
                >
                  Reset
                </Button>
                <Button
                  onClick={savePreferences}
                  disabled={!hasChanges || isSaving}
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    'Save Changes'
                  )}
                </Button>
              </div>
            </div>

            {/* Additional Info */}
            <Alert>
              <Mail className="h-4 w-4" />
              <AlertDescription>
                <strong>Note:</strong> Purchase confirmations and legal notices may still be sent even if you opt out of other emails. 
                You can unsubscribe from all non-essential emails using the links in any email we send you.
              </AlertDescription>
            </Alert>
          </>
        )}
      </CardContent>
    </Card>
  )
}
