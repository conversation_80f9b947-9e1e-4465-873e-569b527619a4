"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { useDispatch } from "react-redux"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Coins,
  Plus,
  Lock,
  Crown,
  ShoppingCart,
  AlertTriangle,
  Info,
  Zap,
  Target,
  TrendingUp,
  Gift
} from "lucide-react"
import { formatCredits, creditsToUSD } from "@/lib/credits"
import { CreditPurchaseModal } from "@/components/credits/credit-purchase-modal"
import {
  useGetCreditBalanceQuery,
  useSpendCreditsMutation,
  useGetCreditPackagesQuery
} from "@/store/api/creditsApi"
import { openPurchaseModal } from "@/store/slices/creditSlice"
import { useToast } from "@/hooks/use-toast"
import type { AppDispatch } from "@/lib/store"

interface ReaderCreditDisplayProps {
  chapterInfo?: {
    id: string
    title: string
    order: number
    isPremium: boolean
    creditPrice?: number | null
    requiredTier?: string | null
  }
  accessInfo?: {
    hasAccess: boolean
    reason?: string
    creditPrice?: number | null
    canPurchaseWithCredits?: boolean
    userCreditBalance?: number
    hasPurchased?: boolean
    purchaseOptions?: {
      credits: {
        available: boolean
        price: number
        canAfford: boolean
        userBalance: number
      }
    }
  }
  onAccessGranted?: () => void
  className?: string
}

export function ReaderCreditDisplay({
  chapterInfo,
  accessInfo,
  onAccessGranted,
  className
}: ReaderCreditDisplayProps) {
  const { data: session } = useSession()
  const { toast } = useToast()
  const dispatch = useDispatch<AppDispatch>()
  const [showPurchaseModal, setShowPurchaseModal] = useState(false)
  const [isPurchasing, setIsPurchasing] = useState(false)

  // Get current credit balance
  const {
    data: balanceData,
    isLoading: isLoadingBalance,
    refetch: refetchBalance
  } = useGetCreditBalanceQuery(undefined, {
    skip: !session?.user,
    pollingInterval: 30000, // Poll every 30 seconds
  })

  // Get credit packages for recommendations
  const { data: packagesData } = useGetCreditPackagesQuery(undefined, {
    skip: !session?.user,
  })

  const [spendCredits] = useSpendCreditsMutation()

  const currentBalance = balanceData?.balance ?? accessInfo?.userCreditBalance ?? 0
  const packages = packagesData?.packages || []
  const requiredCredits = chapterInfo?.creditPrice || accessInfo?.creditPrice || 0

  // If user is not authenticated, show sign-in prompt
  if (!session?.user) {
    return (
      <Card className={`border-blue-200 bg-blue-50 ${className}`}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <Info className="h-5 w-5" />
            Sign In Required
          </CardTitle>
          <CardDescription className="text-blue-600">
            Sign in to view your credit balance and unlock premium content
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  // Generate smart recommendations
  const getTopUpRecommendation = () => {
    if (!requiredCredits || currentBalance >= requiredCredits) return null

    const shortfall = requiredCredits - currentBalance
    const recommendedPackage = packages.find(pkg =>
      (pkg.credits + pkg.bonusCredits) >= shortfall
    )

    if (recommendedPackage) {
      const totalCredits = recommendedPackage.credits + recommendedPackage.bonusCredits
      const leftoverCredits = totalCredits - shortfall

      return {
        package: recommendedPackage,
        shortfall,
        leftoverCredits,
        isExactMatch: leftoverCredits < 10,
        savings: recommendedPackage.bonusCredits * 0.1 // Assuming $0.10 per credit
      }
    }

    return null
  }

  const recommendation = getTopUpRecommendation()

  const handlePurchaseWithCredits = async () => {
    if (!chapterInfo?.id || !accessInfo?.creditPrice) return

    try {
      setIsPurchasing(true)

      const result = await spendCredits({
        contentType: 'CHAPTER',
        contentId: chapterInfo.id,
      }).unwrap()

      toast({
        title: "Chapter Unlocked!",
        description: `You spent ${formatCredits(result.creditsSpent)} to unlock "${chapterInfo.title}"`,
      })

      // Refetch balance and notify parent
      await refetchBalance()
      onAccessGranted?.()

    } catch (error: any) {
      console.error('Error purchasing chapter:', error)
      toast({
        title: "Purchase Failed",
        description: error.data?.error || "Failed to purchase chapter. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsPurchasing(false)
    }
  }

  const handleTopUpClick = (packageId?: string) => {
    dispatch(openPurchaseModal(packageId || null))
  }

  const needsCredits = chapterInfo?.isPremium && !accessInfo?.hasAccess && accessInfo?.canPurchaseWithCredits
  const canAfford = accessInfo?.purchaseOptions?.credits.canAfford ?? false
  const creditPrice = accessInfo?.creditPrice ?? chapterInfo?.creditPrice ?? 0

  return (
    <>
      <Card className={`${className}`}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <Coins className="h-5 w-5 text-yellow-500" />
            Your Credits
          </CardTitle>
          <CardDescription>
            Use credits to unlock premium chapters
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Balance */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Current Balance:</span>
            {isLoadingBalance ? (
              <Skeleton className="h-6 w-16" />
            ) : (
              <div className="flex items-center gap-2">
                <Badge
                  variant={currentBalance < 10 ? "destructive" : currentBalance < 50 ? "secondary" : "default"}
                  className="text-lg font-bold px-3 py-1"
                >
                  {formatCredits(currentBalance)}
                </Badge>
                <span className="text-xs text-muted-foreground">
                  (${creditsToUSD(currentBalance).toFixed(2)})
                </span>
              </div>
            )}
          </div>

          {/* Smart Top-up Recommendation */}
          {recommendation && !canAfford && (
            <Alert className="border-blue-200 bg-blue-50">
              <Target className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800">
                <div className="font-medium mb-2">Smart Recommendation</div>
                <div className="text-sm mb-3">
                  You need {formatCredits(recommendation.shortfall)} more. Get the{" "}
                  <span className="font-medium">{recommendation.package.name}</span> for{" "}
                  ${recommendation.package.price} and have {formatCredits(recommendation.leftoverCredits)} left over.
                  {recommendation.package.bonusCredits > 0 && (
                    <span className="text-green-600 ml-1">
                      (+{formatCredits(recommendation.package.bonusCredits)} bonus)
                    </span>
                  )}
                </div>
                <Button
                  size="sm"
                  onClick={() => handleTopUpClick(recommendation.package.id)}
                  className="w-full"
                >
                  <Gift className="h-4 w-4 mr-2" />
                  Get {recommendation.package.name} - ${recommendation.package.price}
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {/* Chapter Credit Requirement */}
          {chapterInfo?.isPremium && (
            <div className="p-3 border rounded-lg bg-muted/50">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Lock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">This Chapter</span>
                </div>
                {creditPrice > 0 && (
                  <Badge variant="outline" className="text-yellow-600">
                    {formatCredits(creditPrice)}
                  </Badge>
                )}
              </div>
              
              {accessInfo?.hasAccess ? (
                <div className="flex items-center gap-2 text-green-600">
                  <Zap className="h-4 w-4" />
                  <span className="text-sm">You have access to this chapter</span>
                </div>
              ) : accessInfo?.hasPurchased ? (
                <div className="flex items-center gap-2 text-green-600">
                  <ShoppingCart className="h-4 w-4" />
                  <span className="text-sm">Already purchased</span>
                </div>
              ) : needsCredits ? (
                <div className="space-y-2">
                  {canAfford ? (
                    <Button 
                      onClick={handlePurchaseWithCredits}
                      disabled={isPurchasing}
                      size="sm"
                      className="w-full"
                    >
                      {isPurchasing ? (
                        <>
                          <Zap className="h-4 w-4 mr-2 animate-pulse" />
                          Unlocking...
                        </>
                      ) : (
                        <>
                          <ShoppingCart className="h-4 w-4 mr-2" />
                          Unlock for {formatCredits(creditPrice)}
                        </>
                      )}
                    </Button>
                  ) : (
                    <div className="space-y-2">
                      <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                          You need {formatCredits(creditPrice - currentBalance)} more credits
                        </AlertDescription>
                      </Alert>
                      <Button
                        onClick={() => handleTopUpClick()}
                        size="sm"
                        variant="outline"
                        className="w-full"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Top Up Credits
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Crown className="h-4 w-4" />
                  <span className="text-sm">Requires subscription</span>
                </div>
              )}
            </div>
          )}

          {/* General Top Up Section */}
          {!needsCredits && (
            <div className="pt-2 border-t">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Need more credits?</span>
                {currentBalance < 50 && (
                  <Badge variant="outline" className="text-xs">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    Value packs available
                  </Badge>
                )}
              </div>
              <Button
                onClick={() => handleTopUpClick()}
                variant="outline"
                size="sm"
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                Buy More Credits
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Credit Purchase Modal */}
      <CreditPurchaseModal />
    </>
  )
}
