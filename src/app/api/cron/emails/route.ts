import { NextRequest, NextResponse } from "next/server"
import { EmailScheduler } from "@/lib/notifications/email-scheduler"

// GET /api/cron/emails - Run scheduled email jobs (for cron services like Vercel Cron)
export async function GET(request: NextRequest) {
  try {
    // Verify the request is from a cron service
    const authHeader = request.headers.get('authorization')
    const cronSecret = process.env.CRON_SECRET

    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const job = searchParams.get('job') || 'all'

    console.log(`🕐 Running scheduled email job: ${job}`)

    let result: string
    const startTime = Date.now()

    switch (job) {
      case 'low_balance':
        await EmailScheduler.scheduleWeeklyLowBalanceCheck()
        result = 'Low balance warnings sent'
        break

      case 'purchase_reminders':
        await EmailScheduler.schedulePurchaseReminders()
        result = 'Purchase reminders sent'
        break

      case 'weekly_digest':
        await EmailScheduler.sendWeeklyDigest()
        result = 'Weekly digest emails sent'
        break

      case 'all':
      default:
        await EmailScheduler.runScheduledJobs()
        result = 'All scheduled email jobs completed'
        break
    }

    const duration = Date.now() - startTime

    console.log(`✅ Email job '${job}' completed in ${duration}ms`)

    return NextResponse.json({
      success: true,
      job,
      result,
      duration,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("❌ Scheduled email job failed:", error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

// POST /api/cron/emails - Manual trigger for scheduled jobs (with authentication)
export async function POST(request: NextRequest) {
  try {
    // This endpoint can be used for manual triggering or webhook-based scheduling
    const body = await request.json()
    const { job, secret } = body

    // Verify secret for manual triggers
    if (secret !== process.env.CRON_SECRET) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    console.log(`🔧 Manually triggering email job: ${job}`)

    let result: string
    const startTime = Date.now()

    switch (job) {
      case 'low_balance':
        await EmailScheduler.scheduleWeeklyLowBalanceCheck()
        result = 'Low balance warnings sent'
        break

      case 'purchase_reminders':
        await EmailScheduler.schedulePurchaseReminders()
        result = 'Purchase reminders sent'
        break

      case 'weekly_digest':
        await EmailScheduler.sendWeeklyDigest()
        result = 'Weekly digest emails sent'
        break

      case 'all':
        await EmailScheduler.runScheduledJobs()
        result = 'All scheduled email jobs completed'
        break

      default:
        return NextResponse.json(
          { error: "Invalid job type" },
          { status: 400 }
        )
    }

    const duration = Date.now() - startTime

    console.log(`✅ Manual email job '${job}' completed in ${duration}ms`)

    return NextResponse.json({
      success: true,
      job,
      result,
      duration,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("❌ Manual email job failed:", error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
