import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { CreditPaymentService } from "@/lib/payment/credit-payment-service"
import { z } from "zod"

const attachPaymentMethodSchema = z.object({
  paymentMethodId: z.string(),
})

const detachPaymentMethodSchema = z.object({
  paymentMethodId: z.string(),
})

// GET /api/credits/payment-methods - Get user's saved payment methods
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get user's Stripe customer ID
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { stripeCustomerId: true }
    })

    if (!user?.stripeCustomerId) {
      return NextResponse.json({ paymentMethods: [] })
    }

    const paymentMethods = await CreditPaymentService.getUserPaymentMethods(
      user.stripeCustomerId
    )

    return NextResponse.json({ paymentMethods })
  } catch (error) {
    console.error("Error fetching payment methods:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/credits/payment-methods - Attach payment method to customer
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { paymentMethodId } = attachPaymentMethodSchema.parse(body)

    // Get or create Stripe customer
    const stripeCustomerId = await CreditPaymentService.getOrCreateStripeCustomer(
      session.user.id,
      session.user.email!
    )

    await CreditPaymentService.attachPaymentMethodToCustomer(
      paymentMethodId,
      stripeCustomerId
    )

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error attaching payment method:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Failed to save payment method" },
      { status: 500 }
    )
  }
}

// DELETE /api/credits/payment-methods - Detach payment method
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { paymentMethodId } = detachPaymentMethodSchema.parse(body)

    await CreditPaymentService.detachPaymentMethod(paymentMethodId)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error detaching payment method:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Failed to remove payment method" },
      { status: 500 }
    )
  }
}
