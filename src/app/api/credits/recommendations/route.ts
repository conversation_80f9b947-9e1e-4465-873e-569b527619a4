import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { CreditRecommendationEngine } from "@/lib/recommendations/credit-recommendations"
import { z } from "zod"

const getRecommendationsSchema = z.object({
  requiredCredits: z.number().optional(),
  context: z.enum(['chapter_access', 'novel_unlock', 'subscription_renewal', 'general']).optional(),
  metadata: z.record(z.any()).optional(),
})

// GET /api/credits/recommendations - Get personalized credit package recommendations
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const requiredCredits = searchParams.get('requiredCredits') 
      ? parseInt(searchParams.get('requiredCredits')!) 
      : undefined
    const context = searchParams.get('context') as any || 'general'
    
    // Parse metadata if provided
    let metadata = {}
    try {
      const metadataParam = searchParams.get('metadata')
      if (metadataParam) {
        metadata = JSON.parse(metadataParam)
      }
    } catch (error) {
      // Invalid metadata, ignore
    }

    // Validate input
    const validatedInput = getRecommendationsSchema.parse({
      requiredCredits,
      context,
      metadata
    })

    // Get user's current balance
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { creditBalance: true }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Get recommendations based on context
    let recommendations
    if (validatedInput.context && validatedInput.context !== 'general') {
      recommendations = await CreditRecommendationEngine.getContextualRecommendations(
        session.user.id,
        validatedInput.context,
        validatedInput.metadata
      )
    } else {
      recommendations = await CreditRecommendationEngine.getRecommendations(
        session.user.id,
        user.creditBalance,
        validatedInput.requiredCredits,
        validatedInput.context
      )
    }

    // Add additional context for each recommendation
    const enrichedRecommendations = recommendations.map(rec => ({
      ...rec,
      currentBalance: user.creditBalance,
      valuePerCredit: rec.package.price / (rec.package.credits + rec.package.bonusCredits),
      estimatedUsageDays: rec.package.credits / 5, // Assuming 5 credits per day average
      popularityRank: rec.package.isPopular ? 1 : rec.package.isBestValue ? 2 : 3
    }))

    return NextResponse.json({
      recommendations: enrichedRecommendations,
      userContext: {
        currentBalance: user.creditBalance,
        requiredCredits: validatedInput.requiredCredits,
        context: validatedInput.context
      }
    })

  } catch (error) {
    console.error("Error getting credit recommendations:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request parameters", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/credits/recommendations/feedback - Track recommendation feedback
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { 
      recommendationId, 
      packageId, 
      action, 
      context 
    } = z.object({
      recommendationId: z.string().optional(),
      packageId: z.string(),
      action: z.enum(['viewed', 'clicked', 'purchased', 'dismissed']),
      context: z.string().optional()
    }).parse(body)

    // Log recommendation feedback for analytics
    await prisma.recommendationFeedback.create({
      data: {
        userId: session.user.id,
        packageId,
        action,
        context: context || 'unknown',
        metadata: {
          recommendationId,
          timestamp: new Date().toISOString()
        }
      }
    }).catch(error => {
      // Handle case where RecommendationFeedback model doesn't exist yet
      console.log('Recommendation feedback logged (no DB model):', {
        userId: session.user.id,
        packageId,
        action,
        context,
        error: error.message
      })
    })

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error("Error tracking recommendation feedback:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
