import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { CreditPaymentService } from "@/lib/payment/credit-payment-service"
import { z } from "zod"

const purchaseCreditsSchema = z.object({
  packageId: z.string(),
  paymentMethodId: z.string().optional(),
  savePaymentMethod: z.boolean().optional().default(false),
  useStoredPaymentMethod: z.boolean().optional().default(false),
  storedPaymentMethodId: z.string().optional(),
})

// POST /api/credits/purchase - Purchase credits
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const purchaseRequest = purchaseCreditsSchema.parse(body)

    // Validate payment method requirements
    if (!purchaseRequest.paymentMethodId &&
        !(purchaseRequest.useStoredPaymentMethod && purchaseRequest.storedPaymentMethodId)) {
      return NextResponse.json({
        error: "Payment method required",
        code: "PAYMENT_METHOD_REQUIRED"
      }, { status: 400 })
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { id: true, email: true, stripeCustomerId: true }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Get or create Stripe customer
    const stripeCustomerId = await CreditPaymentService.getOrCreateStripeCustomer(
      user.id,
      user.email
    )

    // Process payment using enhanced service
    const result = await CreditPaymentService.createPaymentIntent(
      purchaseRequest,
      user.id,
      stripeCustomerId
    )

    if (result.status === 'failed') {
      return NextResponse.json({
        error: result.error || "Payment processing failed",
        code: "PAYMENT_FAILED"
      }, { status: 400 })
    }

    if (result.status === 'requires_payment_method') {
      return NextResponse.json({
        error: result.error || "Payment method required",
        code: "PAYMENT_METHOD_REQUIRED"
      }, { status: 400 })
    }

    return NextResponse.json({
      purchase: result.purchase,
      clientSecret: result.clientSecret,
      requiresAction: result.requiresAction,
      status: result.status
    })
  } catch (error) {
    console.error("Error purchasing credits:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: "Invalid request data",
          details: error.errors,
          code: "VALIDATION_ERROR"
        },
        { status: 400 }
      )
    }

    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('insufficient_funds')) {
        return NextResponse.json(
          {
            error: "Insufficient funds",
            code: "INSUFFICIENT_FUNDS"
          },
          { status: 400 }
        )
      }

      if (error.message.includes('card_declined')) {
        return NextResponse.json(
          {
            error: "Your card was declined",
            code: "CARD_DECLINED"
          },
          { status: 400 }
        )
      }
    }

    return NextResponse.json(
      {
        error: "Internal server error",
        code: "INTERNAL_ERROR"
      },
      { status: 500 }
    )
  }
}
