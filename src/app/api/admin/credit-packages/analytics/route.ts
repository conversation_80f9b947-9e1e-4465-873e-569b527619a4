import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { UserRole } from "@prisma/client"

// GET /api/admin/credit-packages/analytics - Get credit package analytics (admin only)
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '30')
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000)

    // Get all packages with their metrics
    const packages = await prisma.creditPackage.findMany({
      include: {
        purchases: {
          where: {
            status: 'COMPLETED',
            createdAt: { gte: startDate }
          }
        },
        _count: {
          select: {
            purchases: {
              where: {
                status: 'COMPLETED'
              }
            }
          }
        }
      },
      orderBy: { sortOrder: 'asc' }
    })

    // Calculate package analytics
    const packageAnalytics = packages.map(pkg => {
      const recentPurchases = pkg.purchases.length
      const totalRevenue = pkg.purchases.reduce((sum, purchase) => 
        sum + purchase.amount.toNumber(), 0
      )
      const totalCreditsDistributed = pkg.purchases.reduce((sum, purchase) => 
        sum + purchase.totalCredits, 0
      )

      return {
        id: pkg.id,
        name: pkg.name,
        credits: pkg.credits,
        bonusCredits: pkg.bonusCredits,
        price: pkg.price.toNumber(),
        isActive: pkg.isActive,
        isPopular: pkg.isPopular,
        isBestValue: pkg.isBestValue,
        sortOrder: pkg.sortOrder,
        metrics: {
          totalPurchases: pkg._count.purchases,
          recentPurchases,
          totalRevenue,
          totalCreditsDistributed,
          averageRevenuePerPurchase: recentPurchases > 0 ? totalRevenue / recentPurchases : 0,
          conversionRate: 0, // Would need additional data to calculate
          valuePerCredit: pkg.price.toNumber() / (pkg.credits + pkg.bonusCredits),
          bonusPercentage: pkg.bonusCredits > 0 ? Math.round((pkg.bonusCredits / pkg.credits) * 100) : 0
        }
      }
    })

    // Calculate overall analytics
    const totalPurchases = packageAnalytics.reduce((sum, pkg) => sum + pkg.metrics.recentPurchases, 0)
    const totalRevenue = packageAnalytics.reduce((sum, pkg) => sum + pkg.metrics.totalRevenue, 0)
    const totalCreditsDistributed = packageAnalytics.reduce((sum, pkg) => sum + pkg.metrics.totalCreditsDistributed, 0)

    // Get top performing packages
    const topByRevenue = [...packageAnalytics]
      .sort((a, b) => b.metrics.totalRevenue - a.metrics.totalRevenue)
      .slice(0, 3)

    const topByPurchases = [...packageAnalytics]
      .sort((a, b) => b.metrics.recentPurchases - a.metrics.recentPurchases)
      .slice(0, 3)

    // Get daily purchase trends
    const dailyTrends = await prisma.creditPurchase.groupBy({
      by: ['createdAt'],
      where: {
        status: 'COMPLETED',
        createdAt: { gte: startDate }
      },
      _count: {
        id: true
      },
      _sum: {
        amount: true,
        totalCredits: true
      }
    })

    // Process daily trends
    const trendsByDay = dailyTrends.reduce((acc, trend) => {
      const day = trend.createdAt.toISOString().split('T')[0]
      if (!acc[day]) {
        acc[day] = {
          date: day,
          purchases: 0,
          revenue: 0,
          credits: 0
        }
      }
      acc[day].purchases += trend._count.id
      acc[day].revenue += trend._sum.amount?.toNumber() || 0
      acc[day].credits += trend._sum.totalCredits || 0
      return acc
    }, {} as Record<string, any>)

    const dailyTrendsArray = Object.values(trendsByDay).sort((a: any, b: any) => 
      new Date(a.date).getTime() - new Date(b.date).getTime()
    )

    // Package performance comparison
    const packageComparison = packageAnalytics.map(pkg => ({
      name: pkg.name,
      marketShare: totalPurchases > 0 ? (pkg.metrics.recentPurchases / totalPurchases) * 100 : 0,
      revenueShare: totalRevenue > 0 ? (pkg.metrics.totalRevenue / totalRevenue) * 100 : 0,
      efficiency: pkg.metrics.valuePerCredit,
      popularity: pkg.metrics.recentPurchases
    }))

    return NextResponse.json({
      summary: {
        totalPackages: packages.length,
        activePackages: packages.filter(p => p.isActive).length,
        totalPurchases,
        totalRevenue,
        totalCreditsDistributed,
        averageOrderValue: totalPurchases > 0 ? totalRevenue / totalPurchases : 0,
        period: `${days} days`
      },
      packages: packageAnalytics,
      topPerformers: {
        byRevenue: topByRevenue,
        byPurchases: topByPurchases
      },
      trends: {
        daily: dailyTrendsArray,
        comparison: packageComparison
      },
      insights: {
        mostPopularPackage: topByPurchases[0]?.name || null,
        highestRevenuePackage: topByRevenue[0]?.name || null,
        bestValuePackage: packageAnalytics
          .filter(p => p.bonusCredits > 0)
          .sort((a, b) => b.metrics.bonusPercentage - a.metrics.bonusPercentage)[0]?.name || null,
        averageBonusPercentage: packageAnalytics
          .filter(p => p.bonusCredits > 0)
          .reduce((sum, p) => sum + p.metrics.bonusPercentage, 0) / 
          packageAnalytics.filter(p => p.bonusCredits > 0).length || 0
      }
    })
  } catch (error) {
    console.error("Error fetching package analytics:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
