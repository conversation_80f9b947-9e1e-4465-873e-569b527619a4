import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { UserRole } from "@prisma/client"
import { z } from "zod"

const createCreditPackageSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().min(1).max(500),
  credits: z.number().int().min(1).max(10000),
  bonusCredits: z.number().int().min(0).max(5000).default(0),
  price: z.number().min(0.01).max(1000),
  currency: z.string().length(3).default("usd"),
  sortOrder: z.number().int().min(1).max(100).default(1),
  isActive: z.boolean().default(true),
  isPopular: z.boolean().default(false),
  isBestValue: z.boolean().default(false),
})

const updateCreditPackageSchema = createCreditPackageSchema.partial()

// GET /api/admin/credit-packages - Get all credit packages (admin only)
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const includeInactive = searchParams.get('includeInactive') === 'true'

    const packages = await prisma.creditPackage.findMany({
      where: includeInactive ? {} : { isActive: true },
      orderBy: { sortOrder: 'asc' },
      include: {
        _count: {
          select: {
            purchases: true
          }
        }
      }
    })

    // Calculate additional metrics
    const packagesWithMetrics = await Promise.all(
      packages.map(async (pkg) => {
        const totalRevenue = await prisma.creditPurchase.aggregate({
          where: {
            packageId: pkg.id,
            status: 'COMPLETED'
          },
          _sum: {
            amount: true
          }
        })

        return {
          ...pkg,
          totalPurchases: pkg._count.purchases,
          totalRevenue: totalRevenue._sum.amount || 0,
          valuePerCredit: pkg.price.toNumber() / (pkg.credits + pkg.bonusCredits),
          bonusPercentage: pkg.bonusCredits > 0 ? Math.round((pkg.bonusCredits / pkg.credits) * 100) : 0
        }
      })
    )

    return NextResponse.json({ packages: packagesWithMetrics })
  } catch (error) {
    console.error("Error fetching credit packages:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/admin/credit-packages - Create new credit package (admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const packageData = createCreditPackageSchema.parse(body)

    // Check if name already exists
    const existingPackage = await prisma.creditPackage.findFirst({
      where: { name: packageData.name }
    })

    if (existingPackage) {
      return NextResponse.json(
        { error: "Package name already exists" },
        { status: 400 }
      )
    }

    // If this is marked as popular or best value, unmark others
    if (packageData.isPopular) {
      await prisma.creditPackage.updateMany({
        where: { isPopular: true },
        data: { isPopular: false }
      })
    }

    if (packageData.isBestValue) {
      await prisma.creditPackage.updateMany({
        where: { isBestValue: true },
        data: { isBestValue: false }
      })
    }

    const creditPackage = await prisma.creditPackage.create({
      data: {
        ...packageData,
        price: packageData.price,
      }
    })

    return NextResponse.json({ package: creditPackage }, { status: 201 })
  } catch (error) {
    console.error("Error creating credit package:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// PUT /api/admin/credit-packages - Bulk update package order
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { packages } = z.object({
      packages: z.array(z.object({
        id: z.string(),
        sortOrder: z.number().int().min(1)
      }))
    }).parse(body)

    // Update sort orders in a transaction
    await prisma.$transaction(
      packages.map(pkg => 
        prisma.creditPackage.update({
          where: { id: pkg.id },
          data: { sortOrder: pkg.sortOrder }
        })
      )
    )

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error updating package order:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
