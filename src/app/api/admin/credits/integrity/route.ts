import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { UserRole } from "@prisma/client"
import { CreditTransactionManager } from "@/lib/database/credit-transaction-manager"

// GET /api/admin/credits/integrity - Check credit transaction integrity (admin only)
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const autoFix = searchParams.get('autoFix') === 'true'

    if (userId) {
      // Check specific user
      const result = await CreditTransactionManager.validateTransactionIntegrity(userId)
      
      if (!result.isValid && autoFix) {
        // Auto-fix the discrepancy
        await prisma.user.update({
          where: { id: userId },
          data: { creditBalance: result.expectedBalance }
        })

        // Log the fix
        await prisma.creditTransaction.create({
          data: {
            userId,
            type: 'CREDIT',
            status: 'COMPLETED',
            amount: result.discrepancy,
            description: `Balance correction: Fixed discrepancy of ${result.discrepancy} credits`,
            sourceType: 'system_correction',
            sourceId: 'integrity_check',
            balanceBefore: result.actualBalance,
            balanceAfter: result.expectedBalance,
            metadata: {
              type: 'integrity_fix',
              originalDiscrepancy: result.discrepancy,
              fixedAt: new Date().toISOString()
            }
          }
        })

        return NextResponse.json({
          userId,
          wasFixed: true,
          ...result,
          newBalance: result.expectedBalance
        })
      }

      return NextResponse.json({
        userId,
        wasFixed: false,
        ...result
      })
    }

    // Check all users with credit balances
    const users = await prisma.user.findMany({
      where: {
        creditBalance: { gt: 0 }
      },
      select: { id: true, email: true, creditBalance: true },
      take: 100 // Limit to prevent timeout
    })

    const results = []
    let totalDiscrepancies = 0
    let usersWithIssues = 0

    for (const user of users) {
      try {
        const integrity = await CreditTransactionManager.validateTransactionIntegrity(user.id)
        
        if (!integrity.isValid) {
          usersWithIssues++
          totalDiscrepancies += Math.abs(integrity.discrepancy)
          
          if (autoFix) {
            // Auto-fix the discrepancy
            await prisma.user.update({
              where: { id: user.id },
              data: { creditBalance: integrity.expectedBalance }
            })

            // Log the fix
            await prisma.creditTransaction.create({
              data: {
                userId: user.id,
                type: integrity.discrepancy > 0 ? 'CREDIT' : 'DEBIT',
                status: 'COMPLETED',
                amount: integrity.discrepancy,
                description: `Balance correction: Fixed discrepancy of ${integrity.discrepancy} credits`,
                sourceType: 'system_correction',
                sourceId: 'bulk_integrity_check',
                balanceBefore: integrity.actualBalance,
                balanceAfter: integrity.expectedBalance,
                metadata: {
                  type: 'integrity_fix',
                  originalDiscrepancy: integrity.discrepancy,
                  fixedAt: new Date().toISOString()
                }
              }
            })
          }
        }

        results.push({
          userId: user.id,
          email: user.email,
          ...integrity,
          wasFixed: !integrity.isValid && autoFix
        })
      } catch (error) {
        results.push({
          userId: user.id,
          email: user.email,
          error: error instanceof Error ? error.message : 'Unknown error',
          isValid: false
        })
      }
    }

    return NextResponse.json({
      summary: {
        totalUsersChecked: users.length,
        usersWithIssues,
        totalDiscrepancies,
        autoFixApplied: autoFix
      },
      results: results.filter(r => !r.isValid || r.error) // Only return problematic users
    })

  } catch (error) {
    console.error("Error checking credit integrity:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/admin/credits/integrity - Fix credit balance discrepancies (admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { userIds, action } = body

    if (!Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json(
        { error: "userIds array is required" },
        { status: 400 }
      )
    }

    if (action !== 'fix_discrepancies') {
      return NextResponse.json(
        { error: "Invalid action. Only 'fix_discrepancies' is supported" },
        { status: 400 }
      )
    }

    const results = []

    for (const userId of userIds) {
      try {
        const integrity = await CreditTransactionManager.validateTransactionIntegrity(userId)
        
        if (!integrity.isValid) {
          // Fix the discrepancy
          await prisma.user.update({
            where: { id: userId },
            data: { creditBalance: integrity.expectedBalance }
          })

          // Log the fix
          await prisma.creditTransaction.create({
            data: {
              userId,
              type: integrity.discrepancy > 0 ? 'CREDIT' : 'DEBIT',
              status: 'COMPLETED',
              amount: integrity.discrepancy,
              description: `Manual balance correction: Fixed discrepancy of ${integrity.discrepancy} credits`,
              sourceType: 'admin_correction',
              sourceId: session.user.id,
              balanceBefore: integrity.actualBalance,
              balanceAfter: integrity.expectedBalance,
              metadata: {
                type: 'manual_integrity_fix',
                adminUserId: session.user.id,
                originalDiscrepancy: integrity.discrepancy,
                fixedAt: new Date().toISOString()
              }
            }
          })

          results.push({
            userId,
            success: true,
            discrepancyFixed: integrity.discrepancy,
            newBalance: integrity.expectedBalance
          })
        } else {
          results.push({
            userId,
            success: true,
            message: 'No discrepancy found'
          })
        }
      } catch (error) {
        results.push({
          userId,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return NextResponse.json({
      message: "Integrity fix completed",
      results
    })

  } catch (error) {
    console.error("Error fixing credit integrity:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
