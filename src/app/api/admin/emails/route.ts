import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { UserRole } from "@prisma/client"
import { EmailService } from "@/lib/email/email-service"
import { EmailScheduler } from "@/lib/notifications/email-scheduler"
import { z } from "zod"

const sendTestEmailSchema = z.object({
  to: z.string().email(),
  type: z.enum(['purchase_confirmation', 'low_balance_warning', 'purchase_reminder', 'weekly_digest']),
  data: z.record(z.any()).optional()
})

const triggerScheduledJobSchema = z.object({
  job: z.enum(['low_balance_check', 'purchase_reminders', 'weekly_digest', 'all'])
})

// GET /api/admin/emails - Get email statistics and logs (admin only)
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '30')

    // Get email statistics
    const stats = await EmailService.getEmailStats(days)

    // Get recent email logs (if available)
    let recentEmails = []
    try {
      const { prisma } = await import('@/lib/db')
      recentEmails = await prisma.emailLog.findMany({
        take: 50,
        orderBy: { sentAt: 'desc' },
        where: {
          sentAt: {
            gte: new Date(Date.now() - days * 24 * 60 * 60 * 1000)
          }
        }
      })
    } catch (error) {
      // Email logs table might not exist
      console.log('Email logs not available:', error)
    }

    return NextResponse.json({
      stats,
      recentEmails,
      period: `${days} days`
    })

  } catch (error) {
    console.error("Error fetching email data:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/admin/emails - Send test emails or trigger scheduled jobs (admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const action = body.action

    if (action === 'send_test_email') {
      const { to, type, data } = sendTestEmailSchema.parse(body)
      
      let result
      switch (type) {
        case 'purchase_confirmation':
          const { CreditPurchaseNotificationService } = await import('@/lib/notifications/credit-purchase-notifications')
          result = await CreditPurchaseNotificationService.sendPurchaseConfirmation({
            purchaseId: data?.purchaseId || 'test-purchase-id',
            userEmail: to,
            userName: data?.userName || 'Test User',
            packageName: data?.packageName || 'Test Package',
            credits: data?.credits || 100,
            bonusCredits: data?.bonusCredits || 10,
            totalCredits: data?.totalCredits || 110,
            amount: data?.amount || 10.00,
            currency: data?.currency || 'usd',
            purchaseDate: new Date(),
            stripePaymentId: data?.stripePaymentId || 'test-payment-id'
          })
          break

        case 'low_balance_warning':
          const { CreditPurchaseNotificationService: NotificationService } = await import('@/lib/notifications/credit-purchase-notifications')
          result = await NotificationService.sendLowBalanceWarning({
            userEmail: to,
            userName: data?.userName || 'Test User',
            currentBalance: data?.currentBalance || 5,
            recommendedPackage: data?.recommendedPackage || {
              name: 'Value Pack',
              credits: 100,
              price: 10.00
            }
          })
          break

        case 'purchase_reminder':
          result = await EmailService.sendEmail({
            to,
            subject: 'Test Purchase Reminder',
            html: '<h1>Test Purchase Reminder</h1><p>This is a test email.</p>',
            text: 'Test Purchase Reminder\n\nThis is a test email.'
          })
          break

        case 'weekly_digest':
          result = await EmailService.sendEmail({
            to,
            subject: 'Test Weekly Digest',
            html: '<h1>Test Weekly Digest</h1><p>This is a test weekly digest email.</p>',
            text: 'Test Weekly Digest\n\nThis is a test weekly digest email.'
          })
          break

        default:
          return NextResponse.json(
            { error: "Invalid email type" },
            { status: 400 }
          )
      }

      return NextResponse.json({
        success: true,
        message: `Test ${type} email sent to ${to}`,
        result
      })

    } else if (action === 'trigger_scheduled_job') {
      const { job } = triggerScheduledJobSchema.parse(body)

      let result
      switch (job) {
        case 'low_balance_check':
          await EmailScheduler.scheduleWeeklyLowBalanceCheck()
          result = 'Low balance check completed'
          break

        case 'purchase_reminders':
          await EmailScheduler.schedulePurchaseReminders()
          result = 'Purchase reminders sent'
          break

        case 'weekly_digest':
          await EmailScheduler.sendWeeklyDigest()
          result = 'Weekly digest emails sent'
          break

        case 'all':
          await EmailScheduler.runScheduledJobs()
          result = 'All scheduled jobs completed'
          break

        default:
          return NextResponse.json(
            { error: "Invalid job type" },
            { status: 400 }
          )
      }

      return NextResponse.json({
        success: true,
        message: result
      })

    } else {
      return NextResponse.json(
        { error: "Invalid action" },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error("Error processing email request:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// PUT /api/admin/emails - Update email configuration (admin only)
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { action, config } = body

    if (action === 'update_settings') {
      // Update email settings in environment or database
      // This would typically update configuration like:
      // - Email provider settings
      // - Notification frequency
      // - Template customizations
      
      return NextResponse.json({
        success: true,
        message: "Email settings updated",
        config
      })
    }

    return NextResponse.json(
      { error: "Invalid action" },
      { status: 400 }
    )

  } catch (error) {
    console.error("Error updating email configuration:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
