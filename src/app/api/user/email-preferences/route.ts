import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { z } from "zod"

const updatePreferencesSchema = z.object({
  emailNotifications: z.boolean().optional(),
  lowBalanceWarnings: z.boolean().optional(),
  purchaseConfirmations: z.boolean().optional(),
  weeklyDigest: z.boolean().optional(),
  marketingEmails: z.boolean().optional(),
  purchaseReminders: z.boolean().optional(),
})

// GET /api/user/email-preferences - Get user's email preferences
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        emailNotifications: true,
        lowBalanceWarnings: true,
        purchaseConfirmations: true,
        weeklyDigest: true,
        marketingEmails: true,
        purchaseReminders: true,
      }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Provide defaults for any missing preferences
    const preferences = {
      emailNotifications: user.emailNotifications ?? true,
      lowBalanceWarnings: user.lowBalanceWarnings ?? true,
      purchaseConfirmations: user.purchaseConfirmations ?? true,
      weeklyDigest: user.weeklyDigest ?? false,
      marketingEmails: user.marketingEmails ?? false,
      purchaseReminders: user.purchaseReminders ?? true,
    }

    return NextResponse.json({ preferences })

  } catch (error) {
    console.error("Error fetching email preferences:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// PUT /api/user/email-preferences - Update user's email preferences
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const preferences = updatePreferencesSchema.parse(body)

    // Update user preferences
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: preferences,
      select: {
        emailNotifications: true,
        lowBalanceWarnings: true,
        purchaseConfirmations: true,
        weeklyDigest: true,
        marketingEmails: true,
        purchaseReminders: true,
      }
    })

    // Log the preference change
    await prisma.userActivityLog.create({
      data: {
        userId: session.user.id,
        action: 'EMAIL_PREFERENCES_UPDATED',
        details: JSON.stringify(preferences),
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      }
    }).catch(error => {
      // Don't fail if activity log table doesn't exist
      console.log('Activity log not available:', error.message)
    })

    return NextResponse.json({
      success: true,
      preferences: updatedUser
    })

  } catch (error) {
    console.error("Error updating email preferences:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/user/email-preferences - Unsubscribe from all emails (for email links)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { token, action } = body

    if (action === 'unsubscribe_all') {
      // Verify unsubscribe token (you'd implement token generation/verification)
      if (!token) {
        return NextResponse.json(
          { error: "Invalid unsubscribe token" },
          { status: 400 }
        )
      }

      // Decode token to get user ID (implement your token system)
      // For now, we'll use a simple approach
      try {
        const decoded = Buffer.from(token, 'base64').toString('utf-8')
        const [userId, timestamp] = decoded.split(':')
        
        // Check if token is not too old (e.g., 30 days)
        const tokenAge = Date.now() - parseInt(timestamp)
        if (tokenAge > 30 * 24 * 60 * 60 * 1000) {
          return NextResponse.json(
            { error: "Unsubscribe token expired" },
            { status: 400 }
          )
        }

        // Unsubscribe user from all emails
        await prisma.user.update({
          where: { id: userId },
          data: {
            emailNotifications: false,
            lowBalanceWarnings: false,
            purchaseConfirmations: true, // Keep purchase confirmations for legal reasons
            weeklyDigest: false,
            marketingEmails: false,
            purchaseReminders: false,
          }
        })

        return NextResponse.json({
          success: true,
          message: "Successfully unsubscribed from all emails"
        })

      } catch (error) {
        return NextResponse.json(
          { error: "Invalid unsubscribe token" },
          { status: 400 }
        )
      }
    }

    return NextResponse.json(
      { error: "Invalid action" },
      { status: 400 }
    )

  } catch (error) {
    console.error("Error processing unsubscribe:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// Utility function to generate unsubscribe token
export function generateUnsubscribeToken(userId: string): string {
  const timestamp = Date.now().toString()
  const data = `${userId}:${timestamp}`
  return Buffer.from(data).toString('base64')
}
