import { prisma } from "@/lib/db"
import { PaymentStatus, CreditTransactionType, CreditTransactionStatus } from "@prisma/client"

export interface CreditTransactionData {
  userId: string
  type: CreditTransactionType
  amount: number
  description: string
  sourceType: string
  sourceId: string
  metadata?: Record<string, any>
}

export interface CreditPurchaseTransactionData {
  userId: string
  packageId: string
  credits: number
  bonusCredits: number
  totalCredits: number
  amount: number
  currency: string
  stripePaymentId: string
  status: PaymentStatus
}

export class CreditTransactionManager {
  /**
   * Execute credit purchase with full transaction safety
   */
  static async executeCreditPurchase(
    purchaseData: CreditPurchaseTransactionData
  ): Promise<{ purchase: any; newBalance: number }> {
    return await prisma.$transaction(async (tx) => {
      // 1. Get current user balance with row-level locking
      const user = await tx.user.findUnique({
        where: { id: purchaseData.userId },
        select: { id: true, creditBalance: true, email: true, name: true }
      })

      if (!user) {
        throw new Error('User not found')
      }

      // 2. Get package details
      const creditPackage = await tx.creditPackage.findUnique({
        where: { id: purchaseData.packageId },
        select: { 
          id: true, 
          name: true, 
          description: true, 
          credits: true, 
          bonusCredits: true, 
          price: true, 
          currency: true, 
          isActive: true 
        }
      })

      if (!creditPackage || !creditPackage.isActive) {
        throw new Error('Credit package not found or inactive')
      }

      // 3. Validate purchase data consistency
      if (
        purchaseData.credits !== creditPackage.credits ||
        purchaseData.bonusCredits !== creditPackage.bonusCredits ||
        purchaseData.totalCredits !== (creditPackage.credits + creditPackage.bonusCredits) ||
        purchaseData.amount !== creditPackage.price.toNumber() ||
        purchaseData.currency !== creditPackage.currency
      ) {
        throw new Error('Purchase data inconsistent with package details')
      }

      const currentBalance = user.creditBalance
      const newBalance = currentBalance + purchaseData.totalCredits

      // 4. Create credit purchase record
      const creditPurchase = await tx.creditPurchase.create({
        data: {
          userId: purchaseData.userId,
          packageId: purchaseData.packageId,
          credits: purchaseData.credits,
          bonusCredits: purchaseData.bonusCredits,
          totalCredits: purchaseData.totalCredits,
          amount: purchaseData.amount,
          currency: purchaseData.currency,
          stripePaymentId: purchaseData.stripePaymentId,
          status: purchaseData.status,
        },
        include: {
          package: true,
          user: {
            select: { id: true, email: true, name: true }
          }
        }
      })

      // 5. Update user credit balance (only if payment is completed)
      if (purchaseData.status === PaymentStatus.COMPLETED) {
        await tx.user.update({
          where: { id: purchaseData.userId },
          data: { creditBalance: newBalance }
        })

        // 6. Create credit transaction record
        await tx.creditTransaction.create({
          data: {
            userId: purchaseData.userId,
            type: CreditTransactionType.CREDIT,
            status: CreditTransactionStatus.COMPLETED,
            amount: purchaseData.totalCredits,
            description: `Credit purchase: ${creditPackage.name}`,
            sourceType: 'purchase',
            sourceId: creditPurchase.id,
            balanceBefore: currentBalance,
            balanceAfter: newBalance,
            purchaseId: creditPurchase.id,
            metadata: {
              packageName: creditPackage.name,
              baseCredits: purchaseData.credits,
              bonusCredits: purchaseData.bonusCredits,
              stripePaymentId: purchaseData.stripePaymentId
            }
          }
        })
      }

      return {
        purchase: creditPurchase,
        newBalance: purchaseData.status === PaymentStatus.COMPLETED ? newBalance : currentBalance
      }
    }, {
      maxWait: 10000, // 10 seconds
      timeout: 30000, // 30 seconds
      isolationLevel: 'Serializable' // Highest isolation level for financial transactions
    })
  }

  /**
   * Execute credit spending with transaction safety
   */
  static async executeCreditSpending(
    userId: string,
    amount: number,
    description: string,
    sourceType: string,
    sourceId: string,
    metadata?: Record<string, any>
  ): Promise<{ transaction: any; newBalance: number }> {
    return await prisma.$transaction(async (tx) => {
      // 1. Get current user balance with row-level locking
      const user = await tx.user.findUnique({
        where: { id: userId },
        select: { id: true, creditBalance: true }
      })

      if (!user) {
        throw new Error('User not found')
      }

      const currentBalance = user.creditBalance
      const newBalance = currentBalance - amount

      // 2. Check sufficient balance
      if (newBalance < 0) {
        throw new Error('Insufficient credit balance')
      }

      // 3. Update user balance
      await tx.user.update({
        where: { id: userId },
        data: { creditBalance: newBalance }
      })

      // 4. Create debit transaction record
      const transaction = await tx.creditTransaction.create({
        data: {
          userId,
          type: CreditTransactionType.DEBIT,
          status: CreditTransactionStatus.COMPLETED,
          amount: -amount, // Negative for debit
          description,
          sourceType,
          sourceId,
          balanceBefore: currentBalance,
          balanceAfter: newBalance,
          metadata
        }
      })

      return {
        transaction,
        newBalance
      }
    }, {
      maxWait: 5000, // 5 seconds
      timeout: 15000, // 15 seconds
      isolationLevel: 'Serializable'
    })
  }

  /**
   * Execute credit refund with transaction safety
   */
  static async executeCreditRefund(
    userId: string,
    amount: number,
    description: string,
    sourceType: string,
    sourceId: string,
    metadata?: Record<string, any>
  ): Promise<{ transaction: any; newBalance: number }> {
    return await prisma.$transaction(async (tx) => {
      // 1. Get current user balance
      const user = await tx.user.findUnique({
        where: { id: userId },
        select: { id: true, creditBalance: true }
      })

      if (!user) {
        throw new Error('User not found')
      }

      const currentBalance = user.creditBalance
      const newBalance = currentBalance + amount

      // 2. Update user balance
      await tx.user.update({
        where: { id: userId },
        data: { creditBalance: newBalance }
      })

      // 3. Create credit transaction record
      const transaction = await tx.creditTransaction.create({
        data: {
          userId,
          type: CreditTransactionType.CREDIT,
          status: CreditTransactionStatus.COMPLETED,
          amount,
          description,
          sourceType,
          sourceId,
          balanceBefore: currentBalance,
          balanceAfter: newBalance,
          metadata
        }
      })

      return {
        transaction,
        newBalance
      }
    }, {
      maxWait: 5000,
      timeout: 15000,
      isolationLevel: 'Serializable'
    })
  }

  /**
   * Execute batch credit operations (for bulk operations)
   */
  static async executeBatchCreditOperations(
    operations: Array<{
      userId: string
      type: 'credit' | 'debit'
      amount: number
      description: string
      sourceType: string
      sourceId: string
      metadata?: Record<string, any>
    }>
  ): Promise<Array<{ userId: string; newBalance: number; transaction: any }>> {
    return await prisma.$transaction(async (tx) => {
      const results = []

      for (const operation of operations) {
        // Get current balance
        const user = await tx.user.findUnique({
          where: { id: operation.userId },
          select: { id: true, creditBalance: true }
        })

        if (!user) {
          throw new Error(`User ${operation.userId} not found`)
        }

        const currentBalance = user.creditBalance
        const transactionAmount = operation.type === 'debit' ? -operation.amount : operation.amount
        const newBalance = currentBalance + transactionAmount

        // Validate sufficient balance for debits
        if (operation.type === 'debit' && newBalance < 0) {
          throw new Error(`Insufficient balance for user ${operation.userId}`)
        }

        // Update balance
        await tx.user.update({
          where: { id: operation.userId },
          data: { creditBalance: newBalance }
        })

        // Create transaction record
        const transaction = await tx.creditTransaction.create({
          data: {
            userId: operation.userId,
            type: operation.type === 'debit' ? CreditTransactionType.DEBIT : CreditTransactionType.CREDIT,
            status: CreditTransactionStatus.COMPLETED,
            amount: transactionAmount,
            description: operation.description,
            sourceType: operation.sourceType,
            sourceId: operation.sourceId,
            balanceBefore: currentBalance,
            balanceAfter: newBalance,
            metadata: operation.metadata
          }
        })

        results.push({
          userId: operation.userId,
          newBalance,
          transaction
        })
      }

      return results
    }, {
      maxWait: 15000, // 15 seconds for batch operations
      timeout: 60000, // 1 minute for batch operations
      isolationLevel: 'Serializable'
    })
  }

  /**
   * Get user balance with optional lock
   */
  static async getUserBalance(userId: string, lock = false): Promise<number> {
    if (lock) {
      return await prisma.$transaction(async (tx) => {
        const user = await tx.user.findUnique({
          where: { id: userId },
          select: { creditBalance: true }
        })
        return user?.creditBalance || 0
      }, {
        isolationLevel: 'Serializable'
      })
    } else {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { creditBalance: true }
      })
      return user?.creditBalance || 0
    }
  }

  /**
   * Validate transaction integrity
   */
  static async validateTransactionIntegrity(userId: string): Promise<{
    isValid: boolean
    expectedBalance: number
    actualBalance: number
    discrepancy: number
  }> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { creditBalance: true }
    })

    if (!user) {
      throw new Error('User not found')
    }

    // Calculate expected balance from transactions
    const transactions = await prisma.creditTransaction.findMany({
      where: { 
        userId,
        status: CreditTransactionStatus.COMPLETED
      },
      orderBy: { createdAt: 'asc' }
    })

    const expectedBalance = transactions.reduce((balance, transaction) => {
      return balance + transaction.amount
    }, 0)

    const actualBalance = user.creditBalance
    const discrepancy = actualBalance - expectedBalance

    return {
      isValid: Math.abs(discrepancy) < 0.01, // Allow for small floating point differences
      expectedBalance,
      actualBalance,
      discrepancy
    }
  }
}
