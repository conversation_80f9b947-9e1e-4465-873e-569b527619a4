import { prisma } from "@/lib/db"
import { stripe } from "@/lib/stripe"
import { PaymentStatus, CreditTransactionType, CreditTransactionStatus } from "@prisma/client"

export interface TestUser {
  id: string
  email: string
  name: string
  creditBalance: number
  stripeCustomerId?: string
}

export interface TestCreditPackage {
  id: string
  name: string
  credits: number
  bonusCredits: number
  price: number
  currency: string
  isActive: boolean
}

export interface TestPurchaseData {
  userId: string
  packageId: string
  stripePaymentId: string
  status: PaymentStatus
}

export class CreditTestUtils {
  /**
   * Create a test user with specified credit balance
   */
  static async createTestUser(
    email: string = `test-${Date.now()}@example.com`,
    creditBalance: number = 0,
    name: string = 'Test User'
  ): Promise<TestUser> {
    const user = await prisma.user.create({
      data: {
        email,
        name,
        creditBalance,
        emailVerified: new Date(),
      }
    })

    return {
      id: user.id,
      email: user.email,
      name: user.name || 'Test User',
      creditBalance: user.creditBalance,
      stripeCustomerId: user.stripeCustomerId || undefined
    }
  }

  /**
   * Create a test credit package
   */
  static async createTestCreditPackage(
    name: string = `Test Package ${Date.now()}`,
    credits: number = 100,
    bonusCredits: number = 10,
    price: number = 10.00,
    currency: string = 'usd'
  ): Promise<TestCreditPackage> {
    const pkg = await prisma.creditPackage.create({
      data: {
        name,
        description: `Test package with ${credits} credits`,
        credits,
        bonusCredits,
        price,
        currency,
        sortOrder: 1,
        isActive: true,
      }
    })

    return {
      id: pkg.id,
      name: pkg.name,
      credits: pkg.credits,
      bonusCredits: pkg.bonusCredits,
      price: pkg.price.toNumber(),
      currency: pkg.currency,
      isActive: pkg.isActive
    }
  }

  /**
   * Create a test Stripe customer for a user
   */
  static async createTestStripeCustomer(userId: string, email: string): Promise<string> {
    const customer = await stripe.customers.create({
      email,
      metadata: { userId },
    })

    await prisma.user.update({
      where: { id: userId },
      data: { stripeCustomerId: customer.id }
    })

    return customer.id
  }

  /**
   * Create a test payment method in Stripe
   */
  static async createTestPaymentMethod(customerId?: string): Promise<string> {
    const paymentMethod = await stripe.paymentMethods.create({
      type: 'card',
      card: {
        number: '****************', // Stripe test card
        exp_month: 12,
        exp_year: 2025,
        cvc: '123',
      },
    })

    if (customerId) {
      await stripe.paymentMethods.attach(paymentMethod.id, {
        customer: customerId,
      })
    }

    return paymentMethod.id
  }

  /**
   * Create a test payment intent
   */
  static async createTestPaymentIntent(
    amount: number,
    currency: string = 'usd',
    customerId?: string,
    paymentMethodId?: string
  ): Promise<string> {
    const paymentIntentData: any = {
      amount: Math.round(amount * 100), // Convert to cents
      currency,
      confirmation_method: 'manual',
      confirm: true,
    }

    if (customerId) {
      paymentIntentData.customer = customerId
    }

    if (paymentMethodId) {
      paymentIntentData.payment_method = paymentMethodId
    }

    const paymentIntent = await stripe.paymentIntents.create(paymentIntentData)
    return paymentIntent.id
  }

  /**
   * Simulate a successful credit purchase
   */
  static async simulateCreditPurchase(
    userId: string,
    packageId: string,
    stripePaymentId?: string
  ): Promise<any> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { creditBalance: true }
    })

    const pkg = await prisma.creditPackage.findUnique({
      where: { id: packageId }
    })

    if (!user || !pkg) {
      throw new Error('User or package not found')
    }

    const totalCredits = pkg.credits + pkg.bonusCredits
    const newBalance = user.creditBalance + totalCredits

    // Create purchase record
    const purchase = await prisma.creditPurchase.create({
      data: {
        userId,
        packageId,
        credits: pkg.credits,
        bonusCredits: pkg.bonusCredits,
        totalCredits,
        amount: pkg.price,
        currency: pkg.currency,
        stripePaymentId: stripePaymentId || `test_pi_${Date.now()}`,
        status: PaymentStatus.COMPLETED,
      },
      include: {
        package: true,
        user: true
      }
    })

    // Update user balance
    await prisma.user.update({
      where: { id: userId },
      data: { creditBalance: newBalance }
    })

    // Create transaction record
    await prisma.creditTransaction.create({
      data: {
        userId,
        type: CreditTransactionType.CREDIT,
        status: CreditTransactionStatus.COMPLETED,
        amount: totalCredits,
        description: `Test credit purchase: ${pkg.name}`,
        sourceType: 'purchase',
        sourceId: purchase.id,
        balanceBefore: user.creditBalance,
        balanceAfter: newBalance,
        purchaseId: purchase.id,
      }
    })

    return purchase
  }

  /**
   * Simulate credit spending
   */
  static async simulateCreditSpending(
    userId: string,
    amount: number,
    description: string = 'Test credit spending'
  ): Promise<any> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { creditBalance: true }
    })

    if (!user) {
      throw new Error('User not found')
    }

    if (user.creditBalance < amount) {
      throw new Error('Insufficient credits')
    }

    const newBalance = user.creditBalance - amount

    // Update user balance
    await prisma.user.update({
      where: { id: userId },
      data: { creditBalance: newBalance }
    })

    // Create transaction record
    const transaction = await prisma.creditTransaction.create({
      data: {
        userId,
        type: CreditTransactionType.DEBIT,
        status: CreditTransactionStatus.COMPLETED,
        amount: -amount,
        description,
        sourceType: 'test_spending',
        sourceId: `test_${Date.now()}`,
        balanceBefore: user.creditBalance,
        balanceAfter: newBalance,
      }
    })

    return transaction
  }

  /**
   * Get user's current credit balance
   */
  static async getUserCreditBalance(userId: string): Promise<number> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { creditBalance: true }
    })

    return user?.creditBalance || 0
  }

  /**
   * Get user's credit transactions
   */
  static async getUserCreditTransactions(userId: string): Promise<any[]> {
    return await prisma.creditTransaction.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    })
  }

  /**
   * Get user's credit purchases
   */
  static async getUserCreditPurchases(userId: string): Promise<any[]> {
    return await prisma.creditPurchase.findMany({
      where: { userId },
      include: { package: true },
      orderBy: { createdAt: 'desc' }
    })
  }

  /**
   * Clean up test data
   */
  static async cleanupTestData(userIds: string[] = [], packageIds: string[] = []): Promise<void> {
    // Delete in correct order to avoid foreign key constraints
    
    if (userIds.length > 0) {
      // Delete credit transactions
      await prisma.creditTransaction.deleteMany({
        where: { userId: { in: userIds } }
      })

      // Delete credit purchases
      await prisma.creditPurchase.deleteMany({
        where: { userId: { in: userIds } }
      })

      // Delete content purchases
      await prisma.contentPurchase.deleteMany({
        where: { userId: { in: userIds } }
      })

      // Delete users
      await prisma.user.deleteMany({
        where: { id: { in: userIds } }
      })
    }

    if (packageIds.length > 0) {
      // Delete credit packages
      await prisma.creditPackage.deleteMany({
        where: { id: { in: packageIds } }
      })
    }
  }

  /**
   * Validate credit balance consistency
   */
  static async validateCreditBalanceConsistency(userId: string): Promise<{
    isConsistent: boolean
    expectedBalance: number
    actualBalance: number
    discrepancy: number
  }> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { creditBalance: true }
    })

    if (!user) {
      throw new Error('User not found')
    }

    const transactions = await prisma.creditTransaction.findMany({
      where: { 
        userId,
        status: CreditTransactionStatus.COMPLETED
      },
      orderBy: { createdAt: 'asc' }
    })

    const expectedBalance = transactions.reduce((balance, transaction) => {
      return balance + transaction.amount
    }, 0)

    const actualBalance = user.creditBalance
    const discrepancy = actualBalance - expectedBalance

    return {
      isConsistent: Math.abs(discrepancy) < 0.01,
      expectedBalance,
      actualBalance,
      discrepancy
    }
  }

  /**
   * Create test scenario with multiple users and packages
   */
  static async createTestScenario(): Promise<{
    users: TestUser[]
    packages: TestCreditPackage[]
  }> {
    const users = await Promise.all([
      this.createTestUser('<EMAIL>', 0, 'User One'),
      this.createTestUser('<EMAIL>', 50, 'User Two'),
      this.createTestUser('<EMAIL>', 200, 'User Three'),
    ])

    const packages = await Promise.all([
      this.createTestCreditPackage('Starter Pack', 50, 5, 5.00),
      this.createTestCreditPackage('Value Pack', 100, 20, 10.00),
      this.createTestCreditPackage('Premium Pack', 250, 50, 25.00),
    ])

    return { users, packages }
  }
}
