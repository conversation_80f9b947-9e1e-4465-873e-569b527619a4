import nodemailer from 'nodemailer'
import { prisma } from '@/lib/db'

export interface EmailOptions {
  to: string | string[]
  subject: string
  html: string
  text?: string
  from?: string
  replyTo?: string
  attachments?: Array<{
    filename: string
    content: Buffer | string
    contentType?: string
  }>
}

export interface EmailProvider {
  name: string
  send: (options: EmailOptions) => Promise<{ messageId: string; success: boolean }>
}

export interface EmailTemplate {
  subject: string
  html: string
  text: string
}

export class EmailService {
  private static provider: EmailProvider | null = null

  /**
   * Initialize email service with the configured provider
   */
  static async initialize(): Promise<void> {
    if (this.provider) return

    // Determine which email provider to use based on environment variables
    if (process.env.SENDGRID_API_KEY) {
      this.provider = await this.createSendGridProvider()
    } else if (process.env.MAILGUN_API_KEY) {
      this.provider = await this.createMailgunProvider()
    } else if (process.env.AWS_SES_REGION) {
      this.provider = await this.createSESProvider()
    } else if (process.env.RESEND_API_KEY) {
      this.provider = await this.createResendProvider()
    } else if (process.env.SMTP_HOST) {
      this.provider = await this.createSMTPProvider()
    } else {
      // Fallback to console logging for development
      this.provider = this.createConsoleProvider()
    }
  }

  /**
   * Send an email using the configured provider
   */
  static async sendEmail(options: EmailOptions): Promise<{ messageId: string; success: boolean }> {
    await this.initialize()

    if (!this.provider) {
      throw new Error('No email provider configured')
    }

    try {
      const result = await this.provider.send({
        ...options,
        from: options.from || process.env.EMAIL_FROM || 'Black Blogs <<EMAIL>>'
      })

      // Log email to database for tracking
      await this.logEmail(options, result)

      return result
    } catch (error) {
      console.error('Email sending failed:', error)
      
      // Log failed email attempt
      await this.logEmail(options, { messageId: '', success: false }, error)
      
      throw error
    }
  }

  /**
   * Send email using template
   */
  static async sendTemplateEmail(
    to: string | string[],
    template: EmailTemplate,
    data: Record<string, any> = {}
  ): Promise<{ messageId: string; success: boolean }> {
    // Simple template variable replacement
    const processTemplate = (content: string) => {
      return content.replace(/\{\{(\w+)\}\}/g, (match, key) => {
        return data[key] || match
      })
    }

    return this.sendEmail({
      to,
      subject: processTemplate(template.subject),
      html: processTemplate(template.html),
      text: processTemplate(template.text)
    })
  }

  /**
   * Create SendGrid provider
   */
  private static async createSendGridProvider(): Promise<EmailProvider> {
    const sgMail = require('@sendgrid/mail')
    sgMail.setApiKey(process.env.SENDGRID_API_KEY)

    return {
      name: 'SendGrid',
      async send(options: EmailOptions) {
        const msg = {
          to: options.to,
          from: options.from,
          subject: options.subject,
          html: options.html,
          text: options.text,
          replyTo: options.replyTo,
          attachments: options.attachments
        }

        const result = await sgMail.send(msg)
        return {
          messageId: result[0].headers['x-message-id'] || 'sendgrid-' + Date.now(),
          success: true
        }
      }
    }
  }

  /**
   * Create Resend provider
   */
  private static async createResendProvider(): Promise<EmailProvider> {
    const { Resend } = require('resend')
    const resend = new Resend(process.env.RESEND_API_KEY)

    return {
      name: 'Resend',
      async send(options: EmailOptions) {
        const result = await resend.emails.send({
          from: options.from,
          to: options.to,
          subject: options.subject,
          html: options.html,
          text: options.text,
          reply_to: options.replyTo,
          attachments: options.attachments
        })

        return {
          messageId: result.data?.id || 'resend-' + Date.now(),
          success: !result.error
        }
      }
    }
  }

  /**
   * Create SMTP provider using nodemailer
   */
  private static async createSMTPProvider(): Promise<EmailProvider> {
    const transporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    })

    return {
      name: 'SMTP',
      async send(options: EmailOptions) {
        const result = await transporter.sendMail({
          from: options.from,
          to: options.to,
          subject: options.subject,
          html: options.html,
          text: options.text,
          replyTo: options.replyTo,
          attachments: options.attachments
        })

        return {
          messageId: result.messageId,
          success: true
        }
      }
    }
  }

  /**
   * Create Mailgun provider
   */
  private static async createMailgunProvider(): Promise<EmailProvider> {
    const formData = require('form-data')
    const Mailgun = require('mailgun.js')
    const mailgun = new Mailgun(formData)
    const mg = mailgun.client({
      username: 'api',
      key: process.env.MAILGUN_API_KEY,
      url: process.env.MAILGUN_URL || 'https://api.mailgun.net'
    })

    return {
      name: 'Mailgun',
      async send(options: EmailOptions) {
        const result = await mg.messages.create(process.env.MAILGUN_DOMAIN, {
          from: options.from,
          to: options.to,
          subject: options.subject,
          html: options.html,
          text: options.text,
          'h:Reply-To': options.replyTo,
          attachment: options.attachments
        })

        return {
          messageId: result.id,
          success: true
        }
      }
    }
  }

  /**
   * Create AWS SES provider
   */
  private static async createSESProvider(): Promise<EmailProvider> {
    const { SESClient, SendEmailCommand } = require('@aws-sdk/client-ses')
    const client = new SESClient({
      region: process.env.AWS_SES_REGION,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
      }
    })

    return {
      name: 'AWS SES',
      async send(options: EmailOptions) {
        const command = new SendEmailCommand({
          Source: options.from,
          Destination: {
            ToAddresses: Array.isArray(options.to) ? options.to : [options.to]
          },
          Message: {
            Subject: { Data: options.subject },
            Body: {
              Html: { Data: options.html },
              Text: { Data: options.text || '' }
            }
          },
          ReplyToAddresses: options.replyTo ? [options.replyTo] : undefined
        })

        const result = await client.send(command)
        return {
          messageId: result.MessageId,
          success: true
        }
      }
    }
  }

  /**
   * Create console provider for development
   */
  private static createConsoleProvider(): EmailProvider {
    return {
      name: 'Console',
      async send(options: EmailOptions) {
        console.log('\n📧 Email would be sent:')
        console.log('To:', options.to)
        console.log('Subject:', options.subject)
        console.log('From:', options.from)
        if (options.text) {
          console.log('\nText Content:')
          console.log(options.text)
        }
        console.log('\nHTML Content:')
        console.log(options.html)
        console.log('\n' + '='.repeat(50))

        return {
          messageId: 'console-' + Date.now(),
          success: true
        }
      }
    }
  }

  /**
   * Log email to database for tracking
   */
  private static async logEmail(
    options: EmailOptions,
    result: { messageId: string; success: boolean },
    error?: any
  ): Promise<void> {
    try {
      await prisma.emailLog.create({
        data: {
          to: Array.isArray(options.to) ? options.to.join(',') : options.to,
          subject: options.subject,
          provider: this.provider?.name || 'Unknown',
          messageId: result.messageId,
          success: result.success,
          error: error ? JSON.stringify(error) : null,
          sentAt: new Date()
        }
      }).catch(() => {
        // Ignore database errors for email logging
        console.log('Email logged (no DB model available)')
      })
    } catch (error) {
      // Don't throw - email logging failures shouldn't break email sending
      console.error('Failed to log email:', error)
    }
  }

  /**
   * Get email sending statistics
   */
  static async getEmailStats(days: number = 30): Promise<{
    totalSent: number
    successRate: number
    failureRate: number
    byProvider: Record<string, number>
  }> {
    try {
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000)
      
      const stats = await prisma.emailLog.groupBy({
        by: ['provider', 'success'],
        where: {
          sentAt: { gte: startDate }
        },
        _count: true
      })

      const totalSent = stats.reduce((sum, stat) => sum + stat._count, 0)
      const successful = stats
        .filter(stat => stat.success)
        .reduce((sum, stat) => sum + stat._count, 0)

      const byProvider = stats.reduce((acc, stat) => {
        acc[stat.provider] = (acc[stat.provider] || 0) + stat._count
        return acc
      }, {} as Record<string, number>)

      return {
        totalSent,
        successRate: totalSent > 0 ? (successful / totalSent) * 100 : 0,
        failureRate: totalSent > 0 ? ((totalSent - successful) / totalSent) * 100 : 0,
        byProvider
      }
    } catch (error) {
      console.error('Failed to get email stats:', error)
      return {
        totalSent: 0,
        successRate: 0,
        failureRate: 0,
        byProvider: {}
      }
    }
  }
}
