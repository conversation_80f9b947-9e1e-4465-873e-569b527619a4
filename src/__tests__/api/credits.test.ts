import { describe, it, expect, beforeEach, afterEach } from '@jest/jest'
import { createMocks } from 'node-mocks-http'
import { getServerSession } from 'next-auth'
import { CreditTestUtils } from '@/lib/testing/credit-test-utils'

// Mock next-auth
jest.mock('next-auth', () => ({
  getServerSession: jest.fn()
}))

// Import API handlers
import { GET as getBalance } from '@/app/api/credits/balance/route'
import { POST as purchaseCredits } from '@/app/api/credits/purchase/route'
import { POST as spendCredits } from '@/app/api/credits/spend/route'
import { GET as getRecommendations } from '@/app/api/credits/recommendations/route'

describe('Credits API Endpoints', () => {
  let testUsers: any[] = []
  let testPackages: any[] = []
  let mockSession: any

  beforeEach(async () => {
    // Create test scenario
    const scenario = await CreditTestUtils.createTestScenario()
    testUsers = scenario.users
    testPackages = scenario.packages

    // Mock session
    mockSession = {
      user: {
        id: testUsers[0].id,
        email: testUsers[0].email,
        name: testUsers[0].name
      }
    }
    ;(getServerSession as jest.Mock).mockResolvedValue(mockSession)
  })

  afterEach(async () => {
    // Clean up test data
    const userIds = testUsers.map(u => u.id)
    const packageIds = testPackages.map(p => p.id)
    await CreditTestUtils.cleanupTestData(userIds, packageIds)
    jest.clearAllMocks()
  })

  describe('GET /api/credits/balance', () => {
    it('should return user credit balance', async () => {
      const { req, res } = createMocks({
        method: 'GET',
      })

      await getBalance(req as any)
      
      // Since we can't easily test the actual response in this setup,
      // we'll test the underlying logic separately
      const balance = await CreditTestUtils.getUserCreditBalance(testUsers[0].id)
      expect(typeof balance).toBe('number')
      expect(balance).toBeGreaterThanOrEqual(0)
    })

    it('should return 401 for unauthenticated requests', async () => {
      ;(getServerSession as jest.Mock).mockResolvedValue(null)

      const { req } = createMocks({
        method: 'GET',
      })

      const response = await getBalance(req as any)
      const data = await response.json()
      
      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })
  })

  describe('POST /api/credits/purchase', () => {
    it('should validate required fields', async () => {
      const { req } = createMocks({
        method: 'POST',
        body: {
          // Missing required fields
        },
      })

      const response = await purchaseCredits(req as any)
      expect(response.status).toBe(400)
    })

    it('should validate package existence', async () => {
      const { req } = createMocks({
        method: 'POST',
        body: {
          packageId: 'non-existent-package',
          paymentMethodId: 'pm_test_123'
        },
      })

      const response = await purchaseCredits(req as any)
      const data = await response.json()
      
      expect(response.status).toBe(400)
      expect(data.error).toContain('package')
    })

    it('should require payment method', async () => {
      const { req } = createMocks({
        method: 'POST',
        body: {
          packageId: testPackages[0].id,
          // Missing payment method
        },
      })

      const response = await purchaseCredits(req as any)
      const data = await response.json()
      
      expect(response.status).toBe(400)
      expect(data.code).toBe('PAYMENT_METHOD_REQUIRED')
    })
  })

  describe('POST /api/credits/spend', () => {
    beforeEach(async () => {
      // Give user some credits for spending tests
      await CreditTestUtils.simulateCreditPurchase(
        testUsers[0].id,
        testPackages[0].id
      )
    })

    it('should validate required fields', async () => {
      const { req } = createMocks({
        method: 'POST',
        body: {
          // Missing required fields
        },
      })

      const response = await spendCredits(req as any)
      expect(response.status).toBe(400)
    })

    it('should validate content type', async () => {
      const { req } = createMocks({
        method: 'POST',
        body: {
          contentType: 'INVALID_TYPE',
          contentId: 'test-content-id'
        },
      })

      const response = await spendCredits(req as any)
      expect(response.status).toBe(400)
    })

    it('should check for sufficient balance', async () => {
      // Use user with 0 credits
      mockSession.user.id = testUsers[0].id // User with 0 credits initially
      
      const { req } = createMocks({
        method: 'POST',
        body: {
          contentType: 'CHAPTER',
          contentId: 'test-chapter-id'
        },
      })

      // This would fail due to insufficient credits or content not found
      const response = await spendCredits(req as any)
      expect(response.status).toBeGreaterThanOrEqual(400)
    })
  })

  describe('GET /api/credits/recommendations', () => {
    it('should return recommendations for authenticated user', async () => {
      const { req } = createMocks({
        method: 'GET',
        query: {
          requiredCredits: '50',
          context: 'general'
        }
      })

      const response = await getRecommendations(req as any)
      
      if (response.status === 200) {
        const data = await response.json()
        expect(data.recommendations).toBeDefined()
        expect(Array.isArray(data.recommendations)).toBe(true)
        expect(data.userContext).toBeDefined()
      }
    })

    it('should handle invalid context gracefully', async () => {
      const { req } = createMocks({
        method: 'GET',
        query: {
          context: 'invalid_context'
        }
      })

      const response = await getRecommendations(req as any)
      expect(response.status).toBe(400)
    })

    it('should return 401 for unauthenticated requests', async () => {
      ;(getServerSession as jest.Mock).mockResolvedValue(null)

      const { req } = createMocks({
        method: 'GET',
      })

      const response = await getRecommendations(req as any)
      expect(response.status).toBe(401)
    })
  })

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      // Mock a database error by using an invalid user ID
      mockSession.user.id = 'invalid-user-id'

      const { req } = createMocks({
        method: 'GET',
      })

      const response = await getBalance(req as any)
      // Should handle the error gracefully, not crash
      expect(response.status).toBeGreaterThanOrEqual(400)
    })

    it('should validate JSON parsing errors', async () => {
      const { req } = createMocks({
        method: 'POST',
        body: 'invalid-json',
      })

      // This would be handled by the framework, but we can test our validation
      try {
        await purchaseCredits(req as any)
      } catch (error) {
        // Should handle JSON parsing errors
        expect(error).toBeDefined()
      }
    })
  })

  describe('Rate Limiting and Security', () => {
    it('should handle multiple concurrent requests safely', async () => {
      const requests = Array.from({ length: 5 }, () => {
        const { req } = createMocks({
          method: 'GET',
        })
        return getBalance(req as any)
      })

      const responses = await Promise.all(requests)
      
      // All requests should complete without errors
      responses.forEach(response => {
        expect(response.status).toBeLessThan(500)
      })
    })

    it('should validate user ownership of resources', async () => {
      // Try to access another user's data
      mockSession.user.id = 'different-user-id'

      const { req } = createMocks({
        method: 'GET',
      })

      const response = await getBalance(req as any)
      // Should either return 0 balance for new user or handle appropriately
      expect(response.status).toBeLessThan(500)
    })
  })

  describe('Data Consistency', () => {
    it('should maintain balance consistency across operations', async () => {
      const userId = testUsers[1].id // User with initial balance
      const initialBalance = await CreditTestUtils.getUserCreditBalance(userId)

      // Perform multiple operations
      await CreditTestUtils.simulateCreditPurchase(userId, testPackages[0].id)
      await CreditTestUtils.simulateCreditSpending(userId, 10)

      // Validate consistency
      const consistency = await CreditTestUtils.validateCreditBalanceConsistency(userId)
      expect(consistency.isConsistent).toBe(true)
    })

    it('should handle transaction rollbacks properly', async () => {
      const userId = testUsers[0].id
      const initialBalance = await CreditTestUtils.getUserCreditBalance(userId)

      try {
        // Attempt an operation that should fail
        await CreditTestUtils.simulateCreditSpending(userId, 1000) // More than available
      } catch (error) {
        // Balance should remain unchanged
        const finalBalance = await CreditTestUtils.getUserCreditBalance(userId)
        expect(finalBalance).toBe(initialBalance)
      }
    })
  })
})
