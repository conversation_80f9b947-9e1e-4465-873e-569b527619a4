import { describe, it, expect, beforeEach, afterEach } from '@jest/jest'
import { CreditTestUtils } from '@/lib/testing/credit-test-utils'
import { CreditTransactionManager } from '@/lib/database/credit-transaction-manager'
import { CreditPaymentService } from '@/lib/payment/credit-payment-service'
import { CreditRecommendationEngine } from '@/lib/recommendations/credit-recommendations'
import { PaymentStatus } from '@prisma/client'

describe('Credit System Integration Tests', () => {
  let testUsers: any[] = []
  let testPackages: any[] = []

  beforeEach(async () => {
    // Create test scenario
    const scenario = await CreditTestUtils.createTestScenario()
    testUsers = scenario.users
    testPackages = scenario.packages
  })

  afterEach(async () => {
    // Clean up test data
    const userIds = testUsers.map(u => u.id)
    const packageIds = testPackages.map(p => p.id)
    await CreditTestUtils.cleanupTestData(userIds, packageIds)
  })

  describe('Credit Purchase Flow', () => {
    it('should successfully purchase credits and update balance', async () => {
      const user = testUsers[0]
      const package = testPackages[0]
      const initialBalance = await CreditTestUtils.getUserCreditBalance(user.id)

      // Simulate credit purchase
      const purchase = await CreditTestUtils.simulateCreditPurchase(
        user.id,
        package.id
      )

      expect(purchase).toBeDefined()
      expect(purchase.status).toBe(PaymentStatus.COMPLETED)
      expect(purchase.totalCredits).toBe(package.credits + package.bonusCredits)

      // Check balance update
      const newBalance = await CreditTestUtils.getUserCreditBalance(user.id)
      expect(newBalance).toBe(initialBalance + purchase.totalCredits)

      // Validate transaction consistency
      const consistency = await CreditTestUtils.validateCreditBalanceConsistency(user.id)
      expect(consistency.isConsistent).toBe(true)
    })

    it('should handle multiple concurrent purchases safely', async () => {
      const user = testUsers[0]
      const package1 = testPackages[0]
      const package2 = testPackages[1]

      // Simulate concurrent purchases
      const [purchase1, purchase2] = await Promise.all([
        CreditTestUtils.simulateCreditPurchase(user.id, package1.id),
        CreditTestUtils.simulateCreditPurchase(user.id, package2.id)
      ])

      expect(purchase1).toBeDefined()
      expect(purchase2).toBeDefined()

      // Check final balance
      const finalBalance = await CreditTestUtils.getUserCreditBalance(user.id)
      const expectedBalance = (package1.credits + package1.bonusCredits) + 
                             (package2.credits + package2.bonusCredits)
      expect(finalBalance).toBe(expectedBalance)

      // Validate consistency
      const consistency = await CreditTestUtils.validateCreditBalanceConsistency(user.id)
      expect(consistency.isConsistent).toBe(true)
    })

    it('should create proper transaction records', async () => {
      const user = testUsers[0]
      const package = testPackages[0]

      await CreditTestUtils.simulateCreditPurchase(user.id, package.id)

      const transactions = await CreditTestUtils.getUserCreditTransactions(user.id)
      expect(transactions).toHaveLength(1)

      const transaction = transactions[0]
      expect(transaction.type).toBe('CREDIT')
      expect(transaction.status).toBe('COMPLETED')
      expect(transaction.amount).toBe(package.credits + package.bonusCredits)
      expect(transaction.sourceType).toBe('purchase')
    })
  })

  describe('Credit Spending Flow', () => {
    it('should successfully spend credits and update balance', async () => {
      const user = testUsers[1] // User with 50 credits
      const spendAmount = 20
      const initialBalance = await CreditTestUtils.getUserCreditBalance(user.id)

      await CreditTestUtils.simulateCreditSpending(user.id, spendAmount)

      const newBalance = await CreditTestUtils.getUserCreditBalance(user.id)
      expect(newBalance).toBe(initialBalance - spendAmount)

      // Validate consistency
      const consistency = await CreditTestUtils.validateCreditBalanceConsistency(user.id)
      expect(consistency.isConsistent).toBe(true)
    })

    it('should prevent spending more credits than available', async () => {
      const user = testUsers[0] // User with 0 credits
      const spendAmount = 10

      await expect(
        CreditTestUtils.simulateCreditSpending(user.id, spendAmount)
      ).rejects.toThrow('Insufficient credits')
    })

    it('should handle concurrent spending safely', async () => {
      const user = testUsers[2] // User with 200 credits
      const spendAmount1 = 50
      const spendAmount2 = 30

      // Simulate concurrent spending
      await Promise.all([
        CreditTestUtils.simulateCreditSpending(user.id, spendAmount1),
        CreditTestUtils.simulateCreditSpending(user.id, spendAmount2)
      ])

      const finalBalance = await CreditTestUtils.getUserCreditBalance(user.id)
      expect(finalBalance).toBe(200 - spendAmount1 - spendAmount2)

      // Validate consistency
      const consistency = await CreditTestUtils.validateCreditBalanceConsistency(user.id)
      expect(consistency.isConsistent).toBe(true)
    })
  })

  describe('Transaction Manager', () => {
    it('should execute credit purchase with full transaction safety', async () => {
      const user = testUsers[0]
      const package = testPackages[0]

      const result = await CreditTransactionManager.executeCreditPurchase({
        userId: user.id,
        packageId: package.id,
        credits: package.credits,
        bonusCredits: package.bonusCredits,
        totalCredits: package.credits + package.bonusCredits,
        amount: package.price,
        currency: package.currency,
        stripePaymentId: `test_pi_${Date.now()}`,
        status: PaymentStatus.COMPLETED
      })

      expect(result.purchase).toBeDefined()
      expect(result.newBalance).toBe(package.credits + package.bonusCredits)

      // Validate consistency
      const consistency = await CreditTestUtils.validateCreditBalanceConsistency(user.id)
      expect(consistency.isConsistent).toBe(true)
    })

    it('should execute credit spending with transaction safety', async () => {
      const user = testUsers[1] // User with 50 credits
      const spendAmount = 25

      const result = await CreditTransactionManager.executeCreditSpending(
        user.id,
        spendAmount,
        'Test spending',
        'test',
        'test_id'
      )

      expect(result.transaction).toBeDefined()
      expect(result.newBalance).toBe(50 - spendAmount)

      // Validate consistency
      const consistency = await CreditTestUtils.validateCreditBalanceConsistency(user.id)
      expect(consistency.isConsistent).toBe(true)
    })

    it('should validate transaction integrity', async () => {
      const user = testUsers[1] // User with 50 credits

      const integrity = await CreditTransactionManager.validateTransactionIntegrity(user.id)
      expect(integrity.isValid).toBe(true)
      expect(integrity.actualBalance).toBe(50)
      expect(integrity.expectedBalance).toBe(50)
      expect(integrity.discrepancy).toBe(0)
    })
  })

  describe('Credit Recommendations', () => {
    it('should generate recommendations for insufficient credits', async () => {
      const user = testUsers[0] // User with 0 credits
      const requiredCredits = 30

      const recommendations = await CreditRecommendationEngine.getRecommendations(
        user.id,
        0,
        requiredCredits
      )

      expect(recommendations).toHaveLength(1)
      expect(recommendations[0].context).toBe('insufficient_credits')
      expect(recommendations[0].priority).toBe('urgent')
    })

    it('should generate recommendations for low balance', async () => {
      const user = testUsers[0] // User with 0 credits

      const recommendations = await CreditRecommendationEngine.getRecommendations(
        user.id,
        10 // Low balance
      )

      expect(recommendations.length).toBeGreaterThan(0)
      expect(recommendations.some(r => r.context === 'low_balance')).toBe(true)
    })

    it('should generate value optimization recommendations', async () => {
      const user = testUsers[1] // User with 50 credits

      const recommendations = await CreditRecommendationEngine.getRecommendations(
        user.id,
        50
      )

      expect(recommendations.length).toBeGreaterThan(0)
      expect(recommendations.some(r => r.context === 'value_optimization')).toBe(true)
    })
  })

  describe('Payment Integration', () => {
    it('should create Stripe customer for user', async () => {
      const user = testUsers[0]

      const customerId = await CreditPaymentService.getOrCreateStripeCustomer(
        user.id,
        user.email
      )

      expect(customerId).toBeDefined()
      expect(typeof customerId).toBe('string')
    })

    it('should get user payment methods', async () => {
      const user = testUsers[0]

      // Create Stripe customer first
      const customerId = await CreditTestUtils.createTestStripeCustomer(
        user.id,
        user.email
      )

      // Create test payment method
      await CreditTestUtils.createTestPaymentMethod(customerId)

      const paymentMethods = await CreditPaymentService.getUserPaymentMethods(customerId)
      expect(Array.isArray(paymentMethods)).toBe(true)
    })
  })

  describe('Error Handling', () => {
    it('should handle invalid package ID gracefully', async () => {
      const user = testUsers[0]
      const invalidPackageId = 'invalid-package-id'

      await expect(
        CreditTransactionManager.executeCreditPurchase({
          userId: user.id,
          packageId: invalidPackageId,
          credits: 100,
          bonusCredits: 10,
          totalCredits: 110,
          amount: 10,
          currency: 'usd',
          stripePaymentId: 'test_pi',
          status: PaymentStatus.COMPLETED
        })
      ).rejects.toThrow()
    })

    it('should handle invalid user ID gracefully', async () => {
      const invalidUserId = 'invalid-user-id'
      const package = testPackages[0]

      await expect(
        CreditTransactionManager.executeCreditPurchase({
          userId: invalidUserId,
          packageId: package.id,
          credits: package.credits,
          bonusCredits: package.bonusCredits,
          totalCredits: package.credits + package.bonusCredits,
          amount: package.price,
          currency: package.currency,
          stripePaymentId: 'test_pi',
          status: PaymentStatus.COMPLETED
        })
      ).rejects.toThrow('User not found')
    })

    it('should handle insufficient balance gracefully', async () => {
      const user = testUsers[0] // User with 0 credits

      await expect(
        CreditTransactionManager.executeCreditSpending(
          user.id,
          100,
          'Test spending',
          'test',
          'test_id'
        )
      ).rejects.toThrow('Insufficient credit balance')
    })
  })
})
