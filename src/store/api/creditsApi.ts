import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import type { 
  CreditTransaction, 
  CreditPurchase, 
  CreditPackage,
  ContentPurchase,
  PaymentStatus,
  CreditTransactionType,
  CreditTransactionStatus,
  ContentType
} from '@prisma/client'

// Types
export interface CreditBalance {
  balance: number
  userId: string
}

export interface CreditTransactionWithDetails extends CreditTransaction {
  contentPurchase?: {
    id: string
    contentType: ContentType
    contentId: string
  }
}

export interface CreditTransactionsResponse {
  transactions: CreditTransactionWithDetails[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export interface CreditPackagesResponse {
  packages: CreditPackage[]
}

export interface CreditPurchaseWithPackage extends CreditPurchase {
  package: CreditPackage
}

export interface PurchaseCreditsRequest {
  packageId: string
  paymentMethodId: string
}

export interface PurchaseCreditsResponse {
  purchase: CreditPurchaseWithPackage
  clientSecret?: string
  requiresAction?: boolean
}

export interface SpendCreditsRequest {
  contentType: 'NOVEL' | 'CHAPTER'
  contentId: string
}

export interface SpendCreditsResponse {
  message: string
  purchase: ContentPurchase
  newBalance: number
  creditsSpent: number
}

export interface CreditTransactionsQueryParams {
  page?: number
  limit?: number
  type?: CreditTransactionType
  startDate?: string
  endDate?: string
}

export interface EarningSummary {
  summary: {
    totalEarnings: number
    totalRevenue: number
    unpaidAmount: number
    totalTransactions: number
    period: {
      days: number
      earnings: number
      revenue: number
      transactions: number
    }
  }
  creditMetrics: {
    totalCreditTransactions: number
    totalCreditRevenue: number
    totalCreditEarnings: number
    averageCreditTransaction: number
    creditEarningsThisPeriod: number
  }
  byType: {
    allTime: Record<string, any>
    period: Record<string, any>
  }
  unpaidEarnings: any[]
  recentPayouts: any[]
  monthlyTrends: any[]
}

export const creditsApi = createApi({
  reducerPath: 'creditsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/credits',
    credentials: 'include',
    prepareHeaders: (headers) => {
      headers.set('Content-Type', 'application/json')
      return headers
    },
  }),
  tagTypes: ['CreditBalance', 'CreditTransactions', 'CreditPackages', 'CreditPurchases', 'EarningSummary', 'PaymentMethods'],
  endpoints: (builder) => ({
    // Get user's credit balance
    getCreditBalance: builder.query<CreditBalance, void>({
      query: () => '/balance',
      providesTags: ['CreditBalance'],
    }),

    // Get credit transaction history
    getCreditTransactions: builder.query<CreditTransactionsResponse, CreditTransactionsQueryParams>({
      query: (params = {}) => ({
        url: '/transactions',
        params: {
          page: params.page?.toString(),
          limit: params.limit?.toString(),
          type: params.type,
          startDate: params.startDate,
          endDate: params.endDate,
        },
      }),
      providesTags: ['CreditTransactions'],
    }),

    // Get available credit packages
    getCreditPackages: builder.query<CreditPackagesResponse, void>({
      query: () => '/packages',
      providesTags: ['CreditPackages'],
    }),

    // Purchase credits
    purchaseCredits: builder.mutation<PurchaseCreditsResponse, PurchaseCreditsRequest>({
      query: (data) => ({
        url: '/purchase',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['CreditBalance', 'CreditTransactions', 'CreditPurchases'],
    }),

    // Spend credits on content
    spendCredits: builder.mutation<SpendCreditsResponse, SpendCreditsRequest>({
      query: (data) => ({
        url: '/spend',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['CreditBalance', 'CreditTransactions'],
    }),

    // Get earnings summary (for authors)
    getEarningSummary: builder.query<EarningSummary, { period?: string }>({
      query: (params = {}) => ({
        url: '/earnings/summary',
        params: {
          period: params.period || '30',
        },
      }),
      providesTags: ['EarningSummary'],
    }),

    // Admin endpoints
    getAdminCreditPackages: builder.query<any, { includeInactive?: boolean }>({
      query: (params = {}) => ({
        url: '/admin/credit-packages',
        params: {
          includeInactive: params.includeInactive?.toString(),
        },
      }),
      providesTags: ['CreditPackages'],
    }),

    getAdminCreditPackage: builder.query<any, string>({
      query: (id) => `/admin/credit-packages/${id}`,
      providesTags: ['CreditPackages'],
    }),

    getCreditPackageAnalytics: builder.query<any, { days?: number }>({
      query: (params = {}) => ({
        url: '/admin/credit-packages/analytics',
        params: {
          days: params.days?.toString(),
        },
      }),
      providesTags: ['CreditPackages'],
    }),

    createCreditPackage: builder.mutation<any, any>({
      query: (data) => ({
        url: '/admin/credit-packages',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['CreditPackages'],
    }),

    updateCreditPackage: builder.mutation<any, { id: string; data: any }>({
      query: ({ id, data }) => ({
        url: `/admin/credit-packages/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['CreditPackages'],
    }),

    updateCreditPackageOrder: builder.mutation<any, { packages: Array<{ id: string; sortOrder: number }> }>({
      query: (data) => ({
        url: '/admin/credit-packages',
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['CreditPackages'],
    }),

    deleteCreditPackage: builder.mutation<any, string>({
      query: (id) => ({
        url: `/admin/credit-packages/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['CreditPackages'],
    }),

    // Payment method management
    getUserPaymentMethods: builder.query<any, void>({
      query: () => '/credits/payment-methods',
      providesTags: ['PaymentMethods'],
    }),

    attachPaymentMethod: builder.mutation<any, { paymentMethodId: string }>({
      query: (data) => ({
        url: '/credits/payment-methods',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['PaymentMethods'],
    }),

    detachPaymentMethod: builder.mutation<any, { paymentMethodId: string }>({
      query: (data) => ({
        url: '/credits/payment-methods',
        method: 'DELETE',
        body: data,
      }),
      invalidatesTags: ['PaymentMethods'],
    }),
  }),
})

export const {
  useGetCreditBalanceQuery,
  useGetCreditTransactionsQuery,
  useGetCreditPackagesQuery,
  usePurchaseCreditsMutation,
  useSpendCreditsMutation,
  useGetEarningSummaryQuery,
  // Admin hooks
  useGetAdminCreditPackagesQuery,
  useGetAdminCreditPackageQuery,
  useGetCreditPackageAnalyticsQuery,
  useCreateCreditPackageMutation,
  useUpdateCreditPackageMutation,
  useUpdateCreditPackageOrderMutation,
  useDeleteCreditPackageMutation,
  // Payment method hooks
  useGetUserPaymentMethodsQuery,
  useAttachPaymentMethodMutation,
  useDetachPaymentMethodMutation,
} = creditsApi
