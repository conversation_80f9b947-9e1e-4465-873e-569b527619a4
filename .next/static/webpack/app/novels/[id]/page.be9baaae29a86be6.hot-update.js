"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/novels/[id]/page",{

/***/ "(app-pages-browser)/./src/components/reader/credit-status-bar.tsx":
/*!*****************************************************!*\
  !*** ./src/components/reader/credit-status-bar.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreditStatusBar: function() { return /* binding */ CreditStatusBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _lib_credits__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/credits */ \"(app-pages-browser)/./src/lib/credits.ts\");\n/* harmony import */ var _components_credits_credit_purchase_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/credits/credit-purchase-modal */ \"(app-pages-browser)/./src/components/credits/credit-purchase-modal.tsx\");\n/* harmony import */ var _store_api_creditsApi__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/store/api/creditsApi */ \"(app-pages-browser)/./src/store/api/creditsApi.ts\");\n/* harmony import */ var _store_slices_creditSlice__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/store/slices/creditSlice */ \"(app-pages-browser)/./src/store/slices/creditSlice.ts\");\n/* __next_internal_client_entry_do_not_use__ CreditStatusBar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CreditStatusBar(param) {\n    let { className, showRecommendations = true, minimumRecommended = 10 } = param;\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_11__.useDispatch)();\n    // Get current credit balance\n    const { data: balanceData, isLoading: isLoadingBalance } = (0,_store_api_creditsApi__WEBPACK_IMPORTED_MODULE_9__.useGetCreditBalanceQuery)(undefined, {\n        skip: !(session === null || session === void 0 ? void 0 : session.user),\n        pollingInterval: 30000\n    });\n    var _balanceData_balance;\n    const currentBalance = (_balanceData_balance = balanceData === null || balanceData === void 0 ? void 0 : balanceData.balance) !== null && _balanceData_balance !== void 0 ? _balanceData_balance : 0;\n    // Don't show for non-authenticated users or authors\n    if (!(session === null || session === void 0 ? void 0 : session.user) || session.user.role === \"AUTHOR\") {\n        return null;\n    }\n    const isLowBalance = currentBalance < minimumRecommended;\n    const isVeryLowBalance = currentBalance < 5;\n    const handleTopUpClick = ()=>{\n        dispatch((0,_store_slices_creditSlice__WEBPACK_IMPORTED_MODULE_10__.openPurchaseModal)(null));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-l-4 \".concat(isVeryLowBalance ? \"border-l-red-500 bg-red-50\" : isLowBalance ? \"border-l-yellow-500 bg-yellow-50\" : \"border-l-green-500 bg-green-50\", \" \").concat(className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 rounded-full \".concat(isVeryLowBalance ? \"bg-red-100\" : isLowBalance ? \"bg-yellow-100\" : \"bg-green-100\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-5 w-5 \".concat(isVeryLowBalance ? \"text-red-600\" : isLowBalance ? \"text-yellow-600\" : \"text-green-600\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Your Credits:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                            lineNumber: 73,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isLoadingBalance ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                                            className: \"h-6 w-16\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                            lineNumber: 75,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: isVeryLowBalance ? \"destructive\" : isLowBalance ? \"secondary\" : \"default\",\n                                                            className: \"text-lg font-bold px-3 py-1\",\n                                                            children: (0,_lib_credits__WEBPACK_IMPORTED_MODULE_7__.formatCredits)(currentBalance)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                            lineNumber: 77,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 17\n                                                }, this),\n                                                showRecommendations && !isLoadingBalance && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground mt-1\",\n                                                    children: isVeryLowBalance ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1 text-red-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                                lineNumber: 90,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Very low balance - consider topping up\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                                lineNumber: 91,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 23\n                                                    }, this) : isLowBalance ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1 text-yellow-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                                lineNumber: 95,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Low balance - you may want to add more credits\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                                lineNumber: 96,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1 text-green-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                                lineNumber: 100,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Good balance for reading premium content\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                                lineNumber: 101,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: handleTopUpClick,\n                                        size: \"sm\",\n                                        variant: isVeryLowBalance ? \"default\" : \"outline\",\n                                        className: isVeryLowBalance ? \"bg-red-600 hover:bg-red-700\" : \"\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this),\n                                            isVeryLowBalance ? \"Top Up Now\" : \"Buy Credits\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        isVeryLowBalance && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                            className: \"mt-3 border-red-200 bg-red-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                    className: \"text-red-800\",\n                                    children: \"Most premium chapters cost 3-10 credits. Consider purchasing more to continue reading.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_credits_credit_purchase_modal__WEBPACK_IMPORTED_MODULE_8__.CreditPurchaseModal, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(CreditStatusBar, \"khj6ef3FkwpCbsTtmBcTKC05EUc=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession,\n        react_redux__WEBPACK_IMPORTED_MODULE_11__.useDispatch,\n        _store_api_creditsApi__WEBPACK_IMPORTED_MODULE_9__.useGetCreditBalanceQuery\n    ];\n});\n_c = CreditStatusBar;\nvar _c;\n$RefreshReg$(_c, \"CreditStatusBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/reader/credit-status-bar.tsx\n"));

/***/ })

});