"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/novels/[id]/page",{

/***/ "(app-pages-browser)/./src/components/reader/credit-status-bar.tsx":
/*!*****************************************************!*\
  !*** ./src/components/reader/credit-status-bar.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreditStatusBar: function() { return /* binding */ CreditStatusBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _lib_credits__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/credits */ \"(app-pages-browser)/./src/lib/credits.ts\");\n/* harmony import */ var _components_credits_credit_purchase_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/credits/credit-purchase-modal */ \"(app-pages-browser)/./src/components/credits/credit-purchase-modal.tsx\");\n/* harmony import */ var _store_api_creditsApi__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/store/api/creditsApi */ \"(app-pages-browser)/./src/store/api/creditsApi.ts\");\n/* __next_internal_client_entry_do_not_use__ CreditStatusBar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction CreditStatusBar(param) {\n    let { className, showRecommendations = true, minimumRecommended = 10 } = param;\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_10__.useDispatch)();\n    // Get current credit balance\n    const { data: balanceData, isLoading: isLoadingBalance, refetch: refetchBalance } = (0,_store_api_creditsApi__WEBPACK_IMPORTED_MODULE_9__.useGetCreditBalanceQuery)(undefined, {\n        skip: !(session === null || session === void 0 ? void 0 : session.user),\n        pollingInterval: 30000\n    });\n    var _balanceData_balance;\n    const currentBalance = (_balanceData_balance = balanceData === null || balanceData === void 0 ? void 0 : balanceData.balance) !== null && _balanceData_balance !== void 0 ? _balanceData_balance : 0;\n    // Don't show for non-authenticated users or authors\n    if (!(session === null || session === void 0 ? void 0 : session.user) || session.user.role === \"AUTHOR\") {\n        return null;\n    }\n    const isLowBalance = currentBalance < minimumRecommended;\n    const isVeryLowBalance = currentBalance < 5;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-l-4 \".concat(isVeryLowBalance ? \"border-l-red-500 bg-red-50\" : isLowBalance ? \"border-l-yellow-500 bg-yellow-50\" : \"border-l-green-500 bg-green-50\", \" \").concat(className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 rounded-full \".concat(isVeryLowBalance ? \"bg-red-100\" : isLowBalance ? \"bg-yellow-100\" : \"bg-green-100\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5 \".concat(isVeryLowBalance ? \"text-red-600\" : isLowBalance ? \"text-yellow-600\" : \"text-green-600\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Your Credits:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                            lineNumber: 70,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isLoadingBalance ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                                            className: \"h-6 w-16\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                            lineNumber: 72,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: isVeryLowBalance ? \"destructive\" : isLowBalance ? \"secondary\" : \"default\",\n                                                            className: \"text-lg font-bold px-3 py-1\",\n                                                            children: (0,_lib_credits__WEBPACK_IMPORTED_MODULE_7__.formatCredits)(currentBalance)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                            lineNumber: 74,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 17\n                                                }, this),\n                                                showRecommendations && !isLoadingBalance && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground mt-1\",\n                                                    children: isVeryLowBalance ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1 text-red-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                                lineNumber: 87,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Very low balance - consider topping up\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                                lineNumber: 88,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 23\n                                                    }, this) : isLowBalance ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1 text-yellow-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                                lineNumber: 92,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Low balance - you may want to add more credits\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                                lineNumber: 93,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1 text-green-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                                lineNumber: 97,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Good balance for reading premium content\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                                lineNumber: 98,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>setShowPurchaseModal(true),\n                                        size: \"sm\",\n                                        variant: isVeryLowBalance ? \"default\" : \"outline\",\n                                        className: isVeryLowBalance ? \"bg-red-600 hover:bg-red-700\" : \"\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this),\n                                            isVeryLowBalance ? \"Top Up Now\" : \"Buy Credits\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        isVeryLowBalance && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                            className: \"mt-3 border-red-200 bg-red-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                    className: \"text-red-800\",\n                                    children: \"Most premium chapters cost 3-10 credits. Consider purchasing more to continue reading.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            showPurchaseModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_credits_credit_purchase_modal__WEBPACK_IMPORTED_MODULE_8__.CreditPurchaseModal, {\n                isOpen: showPurchaseModal,\n                onClose: ()=>setShowPurchaseModal(false),\n                onSuccess: ()=>{\n                    refetchBalance();\n                    setShowPurchaseModal(false);\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(CreditStatusBar, \"uy3ylkQChDKcKezk/WJTOwNdYE0=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession,\n        react_redux__WEBPACK_IMPORTED_MODULE_10__.useDispatch,\n        _store_api_creditsApi__WEBPACK_IMPORTED_MODULE_9__.useGetCreditBalanceQuery\n    ];\n});\n_c = CreditStatusBar;\nvar _c;\n$RefreshReg$(_c, \"CreditStatusBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/reader/credit-status-bar.tsx\n"));

/***/ })

});