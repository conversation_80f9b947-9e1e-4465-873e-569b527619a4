"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/novels/[id]/page",{

/***/ "(app-pages-browser)/./src/components/reader/credit-status-bar.tsx":
/*!*****************************************************!*\
  !*** ./src/components/reader/credit-status-bar.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreditStatusBar: function() { return /* binding */ CreditStatusBar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Coins,Info,Plus,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _lib_credits__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/credits */ \"(app-pages-browser)/./src/lib/credits.ts\");\n/* harmony import */ var _components_credits_credit_purchase_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/credits/credit-purchase-modal */ \"(app-pages-browser)/./src/components/credits/credit-purchase-modal.tsx\");\n/* harmony import */ var _store_api_creditsApi__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/store/api/creditsApi */ \"(app-pages-browser)/./src/store/api/creditsApi.ts\");\n/* __next_internal_client_entry_do_not_use__ CreditStatusBar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction CreditStatusBar(param) {\n    let { className, showRecommendations = true, minimumRecommended = 10 } = param;\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_10__.useDispatch)();\n    // Get current credit balance\n    const { data: balanceData, isLoading: isLoadingBalance } = (0,_store_api_creditsApi__WEBPACK_IMPORTED_MODULE_9__.useGetCreditBalanceQuery)(undefined, {\n        skip: !(session === null || session === void 0 ? void 0 : session.user),\n        pollingInterval: 30000\n    });\n    var _balanceData_balance;\n    const currentBalance = (_balanceData_balance = balanceData === null || balanceData === void 0 ? void 0 : balanceData.balance) !== null && _balanceData_balance !== void 0 ? _balanceData_balance : 0;\n    // Don't show for non-authenticated users or authors\n    if (!(session === null || session === void 0 ? void 0 : session.user) || session.user.role === \"AUTHOR\") {\n        return null;\n    }\n    const isLowBalance = currentBalance < minimumRecommended;\n    const isVeryLowBalance = currentBalance < 5;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-l-4 \".concat(isVeryLowBalance ? \"border-l-red-500 bg-red-50\" : isLowBalance ? \"border-l-yellow-500 bg-yellow-50\" : \"border-l-green-500 bg-green-50\", \" \").concat(className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 rounded-full \".concat(isVeryLowBalance ? \"bg-red-100\" : isLowBalance ? \"bg-yellow-100\" : \"bg-green-100\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5 \".concat(isVeryLowBalance ? \"text-red-600\" : isLowBalance ? \"text-yellow-600\" : \"text-green-600\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Your Credits:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                            lineNumber: 69,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isLoadingBalance ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                                            className: \"h-6 w-16\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                            lineNumber: 71,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: isVeryLowBalance ? \"destructive\" : isLowBalance ? \"secondary\" : \"default\",\n                                                            className: \"text-lg font-bold px-3 py-1\",\n                                                            children: (0,_lib_credits__WEBPACK_IMPORTED_MODULE_7__.formatCredits)(currentBalance)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                            lineNumber: 73,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 17\n                                                }, this),\n                                                showRecommendations && !isLoadingBalance && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground mt-1\",\n                                                    children: isVeryLowBalance ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1 text-red-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                                lineNumber: 86,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Very low balance - consider topping up\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                                lineNumber: 87,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 23\n                                                    }, this) : isLowBalance ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1 text-yellow-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                                lineNumber: 91,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Low balance - you may want to add more credits\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                                lineNumber: 92,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1 text-green-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                                lineNumber: 96,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Good balance for reading premium content\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                                lineNumber: 97,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>setShowPurchaseModal(true),\n                                        size: \"sm\",\n                                        variant: isVeryLowBalance ? \"default\" : \"outline\",\n                                        className: isVeryLowBalance ? \"bg-red-600 hover:bg-red-700\" : \"\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this),\n                                            isVeryLowBalance ? \"Top Up Now\" : \"Buy Credits\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        isVeryLowBalance && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                            className: \"mt-3 border-red-200 bg-red-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Coins_Info_Plus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                    className: \"text-red-800\",\n                                    children: \"Most premium chapters cost 3-10 credits. Consider purchasing more to continue reading.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            showPurchaseModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_credits_credit_purchase_modal__WEBPACK_IMPORTED_MODULE_8__.CreditPurchaseModal, {\n                isOpen: showPurchaseModal,\n                onClose: ()=>setShowPurchaseModal(false),\n                onSuccess: ()=>{\n                    refetchBalance();\n                    setShowPurchaseModal(false);\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/reader/credit-status-bar.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(CreditStatusBar, \"khj6ef3FkwpCbsTtmBcTKC05EUc=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession,\n        react_redux__WEBPACK_IMPORTED_MODULE_10__.useDispatch,\n        _store_api_creditsApi__WEBPACK_IMPORTED_MODULE_9__.useGetCreditBalanceQuery\n    ];\n});\n_c = CreditStatusBar;\nvar _c;\n$RefreshReg$(_c, \"CreditStatusBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3JlYWRlci9jcmVkaXQtc3RhdHVzLWJhci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ0g7QUFDZTtBQUNUO0FBQ0Y7QUFDTTtBQUNZO0FBUTFDO0FBQ3dCO0FBQ21DO0FBQ2Y7QUFVMUQsU0FBU2tCLGdCQUFnQixLQUlUO1FBSlMsRUFDOUJDLFNBQVMsRUFDVEMsc0JBQXNCLElBQUksRUFDMUJDLHFCQUFxQixFQUFFLEVBQ0YsR0FKUzs7SUFLOUIsTUFBTSxFQUFFQyxNQUFNQyxPQUFPLEVBQUUsR0FBR3ZCLDJEQUFVQTtJQUNwQyxNQUFNd0IsV0FBV3ZCLHlEQUFXQTtJQUU1Qiw2QkFBNkI7SUFDN0IsTUFBTSxFQUNKcUIsTUFBTUcsV0FBVyxFQUNqQkMsV0FBV0MsZ0JBQWdCLEVBQzVCLEdBQUdWLCtFQUF3QkEsQ0FBQ1csV0FBVztRQUN0Q0MsTUFBTSxFQUFDTixvQkFBQUEsOEJBQUFBLFFBQVNPLElBQUk7UUFDcEJDLGlCQUFpQjtJQUNuQjtRQUV1Qk47SUFBdkIsTUFBTU8saUJBQWlCUCxDQUFBQSx1QkFBQUEsd0JBQUFBLGtDQUFBQSxZQUFhUSxPQUFPLGNBQXBCUixrQ0FBQUEsdUJBQXdCO0lBRS9DLG9EQUFvRDtJQUNwRCxJQUFJLEVBQUNGLG9CQUFBQSw4QkFBQUEsUUFBU08sSUFBSSxLQUFJUCxRQUFRTyxJQUFJLENBQUNJLElBQUksS0FBSyxVQUFVO1FBQ3BELE9BQU87SUFDVDtJQUVBLE1BQU1DLGVBQWVILGlCQUFpQlg7SUFDdEMsTUFBTWUsbUJBQW1CSixpQkFBaUI7SUFFMUMscUJBQ0U7OzBCQUNFLDhEQUFDOUIscURBQUlBO2dCQUFDaUIsV0FBVyxjQUF3SkEsT0FBMUlpQixtQkFBbUIsK0JBQStCRCxlQUFlLHFDQUFxQyxrQ0FBaUMsS0FBYSxPQUFWaEI7MEJBQ3ZLLDRFQUFDaEIsNERBQVdBO29CQUFDZ0IsV0FBVTs7c0NBQ3JCLDhEQUFDa0I7NEJBQUlsQixXQUFVOzs4Q0FDYiw4REFBQ2tCO29DQUFJbEIsV0FBVTs7c0RBQ2IsOERBQUNrQjs0Q0FBSWxCLFdBQVcsb0JBQXNHLE9BQWxGaUIsbUJBQW1CLGVBQWVELGVBQWUsa0JBQWtCO3NEQUNyRyw0RUFBQzFCLGtJQUFLQTtnREFBQ1UsV0FBVyxXQUFtRyxPQUF4RmlCLG1CQUFtQixpQkFBaUJELGVBQWUsb0JBQW9COzs7Ozs7Ozs7OztzREFHdEcsOERBQUNFOzs4REFDQyw4REFBQ0E7b0RBQUlsQixXQUFVOztzRUFDYiw4REFBQ21COzREQUFLbkIsV0FBVTtzRUFBYzs7Ozs7O3dEQUM3QlEsaUNBQ0MsOERBQUNyQiw2REFBUUE7NERBQUNhLFdBQVU7Ozs7O2lGQUVwQiw4REFBQ2QsdURBQUtBOzREQUNKa0MsU0FBU0gsbUJBQW1CLGdCQUFnQkQsZUFBZSxjQUFjOzREQUN6RWhCLFdBQVU7c0VBRVRKLDJEQUFhQSxDQUFDaUI7Ozs7Ozs7Ozs7OztnREFLcEJaLHVCQUF1QixDQUFDTyxrQ0FDdkIsOERBQUNVO29EQUFJbEIsV0FBVTs4REFDWmlCLGlDQUNDLDhEQUFDQzt3REFBSWxCLFdBQVU7OzBFQUNiLDhEQUFDTCxrSUFBYUE7Z0VBQUNLLFdBQVU7Ozs7OzswRUFDekIsOERBQUNtQjswRUFBSzs7Ozs7Ozs7Ozs7K0RBRU5ILDZCQUNGLDhEQUFDRTt3REFBSWxCLFdBQVU7OzBFQUNiLDhEQUFDUixrSUFBWUE7Z0VBQUNRLFdBQVU7Ozs7OzswRUFDeEIsOERBQUNtQjswRUFBSzs7Ozs7Ozs7Ozs7NkVBR1IsOERBQUNEO3dEQUFJbEIsV0FBVTs7MEVBQ2IsOERBQUNQLGtJQUFVQTtnRUFBQ08sV0FBVTs7Ozs7OzBFQUN0Qiw4REFBQ21COzBFQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FRbEIsOERBQUNEO29DQUFJbEIsV0FBVTs4Q0FDYiw0RUFBQ2YseURBQU1BO3dDQUNMb0MsU0FBUyxJQUFNQyxxQkFBcUI7d0NBQ3BDQyxNQUFLO3dDQUNMSCxTQUFTSCxtQkFBbUIsWUFBWTt3Q0FDeENqQixXQUFXaUIsbUJBQW1CLGdDQUFnQzs7MERBRTlELDhEQUFDMUIsa0lBQUlBO2dEQUFDUyxXQUFVOzs7Ozs7NENBQ2ZpQixtQkFBbUIsZUFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dCQU14Q0Esa0NBQ0MsOERBQUM3Qix1REFBS0E7NEJBQUNZLFdBQVU7OzhDQUNmLDhEQUFDTixrSUFBSUE7b0NBQUNNLFdBQVU7Ozs7Ozs4Q0FDaEIsOERBQUNYLGtFQUFnQkE7b0NBQUNXLFdBQVU7OENBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBU2xEd0IsbUNBQ0MsOERBQUMzQiwwRkFBbUJBO2dCQUNsQjRCLFFBQVFEO2dCQUNSRSxTQUFTLElBQU1KLHFCQUFxQjtnQkFDcENLLFdBQVc7b0JBQ1RDO29CQUNBTixxQkFBcUI7Z0JBQ3ZCOzs7Ozs7OztBQUtWO0dBakhnQnZCOztRQUtZbEIsdURBQVVBO1FBQ25CQyxxREFBV0E7UUFNeEJnQiwyRUFBd0JBOzs7S0FaZEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvcmVhZGVyL2NyZWRpdC1zdGF0dXMtYmFyLnRzeD8wYzI5Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCB7IHVzZVNlc3Npb24gfSBmcm9tIFwibmV4dC1hdXRoL3JlYWN0XCJcbmltcG9ydCB7IHVzZURpc3BhdGNoIH0gZnJvbSBcInJlYWN0LXJlZHV4XCJcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9jYXJkXCJcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCJcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9iYWRnZVwiXG5pbXBvcnQgeyBTa2VsZXRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvc2tlbGV0b25cIlxuaW1wb3J0IHsgQWxlcnQsIEFsZXJ0RGVzY3JpcHRpb24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2FsZXJ0XCJcbmltcG9ydCB7XG4gIENvaW5zLFxuICBQbHVzLFxuICBUcmVuZGluZ0Rvd24sXG4gIFRyZW5kaW5nVXAsXG4gIEluZm8sXG4gIEFsZXJ0VHJpYW5nbGVcbn0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiXG5pbXBvcnQgeyBmb3JtYXRDcmVkaXRzIH0gZnJvbSBcIkAvbGliL2NyZWRpdHNcIlxuaW1wb3J0IHsgQ3JlZGl0UHVyY2hhc2VNb2RhbCB9IGZyb20gXCJAL2NvbXBvbmVudHMvY3JlZGl0cy9jcmVkaXQtcHVyY2hhc2UtbW9kYWxcIlxuaW1wb3J0IHsgdXNlR2V0Q3JlZGl0QmFsYW5jZVF1ZXJ5IH0gZnJvbSBcIkAvc3RvcmUvYXBpL2NyZWRpdHNBcGlcIlxuaW1wb3J0IHsgb3BlblB1cmNoYXNlTW9kYWwgfSBmcm9tIFwiQC9zdG9yZS9zbGljZXMvY3JlZGl0U2xpY2VcIlxuaW1wb3J0IHR5cGUgeyBBcHBEaXNwYXRjaCB9IGZyb20gXCJAL2xpYi9zdG9yZVwiXG5cbmludGVyZmFjZSBDcmVkaXRTdGF0dXNCYXJQcm9wcyB7XG4gIGNsYXNzTmFtZT86IHN0cmluZ1xuICBzaG93UmVjb21tZW5kYXRpb25zPzogYm9vbGVhblxuICBtaW5pbXVtUmVjb21tZW5kZWQ/OiBudW1iZXJcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIENyZWRpdFN0YXR1c0Jhcih7XG4gIGNsYXNzTmFtZSxcbiAgc2hvd1JlY29tbWVuZGF0aW9ucyA9IHRydWUsXG4gIG1pbmltdW1SZWNvbW1lbmRlZCA9IDEwXG59OiBDcmVkaXRTdGF0dXNCYXJQcm9wcykge1xuICBjb25zdCB7IGRhdGE6IHNlc3Npb24gfSA9IHVzZVNlc3Npb24oKVxuICBjb25zdCBkaXNwYXRjaCA9IHVzZURpc3BhdGNoPEFwcERpc3BhdGNoPigpXG5cbiAgLy8gR2V0IGN1cnJlbnQgY3JlZGl0IGJhbGFuY2VcbiAgY29uc3Qge1xuICAgIGRhdGE6IGJhbGFuY2VEYXRhLFxuICAgIGlzTG9hZGluZzogaXNMb2FkaW5nQmFsYW5jZVxuICB9ID0gdXNlR2V0Q3JlZGl0QmFsYW5jZVF1ZXJ5KHVuZGVmaW5lZCwge1xuICAgIHNraXA6ICFzZXNzaW9uPy51c2VyLFxuICAgIHBvbGxpbmdJbnRlcnZhbDogMzAwMDAsIC8vIFBvbGwgZXZlcnkgMzAgc2Vjb25kc1xuICB9KVxuXG4gIGNvbnN0IGN1cnJlbnRCYWxhbmNlID0gYmFsYW5jZURhdGE/LmJhbGFuY2UgPz8gMFxuXG4gIC8vIERvbid0IHNob3cgZm9yIG5vbi1hdXRoZW50aWNhdGVkIHVzZXJzIG9yIGF1dGhvcnNcbiAgaWYgKCFzZXNzaW9uPy51c2VyIHx8IHNlc3Npb24udXNlci5yb2xlID09PSBcIkFVVEhPUlwiKSB7XG4gICAgcmV0dXJuIG51bGxcbiAgfVxuXG4gIGNvbnN0IGlzTG93QmFsYW5jZSA9IGN1cnJlbnRCYWxhbmNlIDwgbWluaW11bVJlY29tbWVuZGVkXG4gIGNvbnN0IGlzVmVyeUxvd0JhbGFuY2UgPSBjdXJyZW50QmFsYW5jZSA8IDVcblxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICA8Q2FyZCBjbGFzc05hbWU9e2Bib3JkZXItbC00ICR7aXNWZXJ5TG93QmFsYW5jZSA/ICdib3JkZXItbC1yZWQtNTAwIGJnLXJlZC01MCcgOiBpc0xvd0JhbGFuY2UgPyAnYm9yZGVyLWwteWVsbG93LTUwMCBiZy15ZWxsb3ctNTAnIDogJ2JvcmRlci1sLWdyZWVuLTUwMCBiZy1ncmVlbi01MCd9ICR7Y2xhc3NOYW1lfWB9PlxuICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BwLTIgcm91bmRlZC1mdWxsICR7aXNWZXJ5TG93QmFsYW5jZSA/ICdiZy1yZWQtMTAwJyA6IGlzTG93QmFsYW5jZSA/ICdiZy15ZWxsb3ctMTAwJyA6ICdiZy1ncmVlbi0xMDAnfWB9PlxuICAgICAgICAgICAgICAgIDxDb2lucyBjbGFzc05hbWU9e2BoLTUgdy01ICR7aXNWZXJ5TG93QmFsYW5jZSA/ICd0ZXh0LXJlZC02MDAnIDogaXNMb3dCYWxhbmNlID8gJ3RleHQteWVsbG93LTYwMCcgOiAndGV4dC1ncmVlbi02MDAnfWB9IC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPllvdXIgQ3JlZGl0czo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICB7aXNMb2FkaW5nQmFsYW5jZSA/IChcbiAgICAgICAgICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtNiB3LTE2XCIgLz5cbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIDxCYWRnZSBcbiAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PXtpc1ZlcnlMb3dCYWxhbmNlID8gXCJkZXN0cnVjdGl2ZVwiIDogaXNMb3dCYWxhbmNlID8gXCJzZWNvbmRhcnlcIiA6IFwiZGVmYXVsdFwifVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHB4LTMgcHktMVwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0Q3JlZGl0cyhjdXJyZW50QmFsYW5jZSl9XG4gICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIHtzaG93UmVjb21tZW5kYXRpb25zICYmICFpc0xvYWRpbmdCYWxhbmNlICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmQgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICB7aXNWZXJ5TG93QmFsYW5jZSA/IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xIHRleHQtcmVkLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEFsZXJ0VHJpYW5nbGUgY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5WZXJ5IGxvdyBiYWxhbmNlIC0gY29uc2lkZXIgdG9wcGluZyB1cDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKSA6IGlzTG93QmFsYW5jZSA/IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xIHRleHQteWVsbG93LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRyZW5kaW5nRG93biBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPkxvdyBiYWxhbmNlIC0geW91IG1heSB3YW50IHRvIGFkZCBtb3JlIGNyZWRpdHM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMSB0ZXh0LWdyZWVuLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRyZW5kaW5nVXAgY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5Hb29kIGJhbGFuY2UgZm9yIHJlYWRpbmcgcHJlbWl1bSBjb250ZW50PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICA8QnV0dG9uIFxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dQdXJjaGFzZU1vZGFsKHRydWUpfVxuICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgdmFyaWFudD17aXNWZXJ5TG93QmFsYW5jZSA/IFwiZGVmYXVsdFwiIDogXCJvdXRsaW5lXCJ9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtpc1ZlcnlMb3dCYWxhbmNlID8gXCJiZy1yZWQtNjAwIGhvdmVyOmJnLXJlZC03MDBcIiA6IFwiXCJ9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIHtpc1ZlcnlMb3dCYWxhbmNlID8gXCJUb3AgVXAgTm93XCIgOiBcIkJ1eSBDcmVkaXRzXCJ9XG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogQWRkaXRpb25hbCBpbmZvIGZvciB2ZXJ5IGxvdyBiYWxhbmNlICovfVxuICAgICAgICAgIHtpc1ZlcnlMb3dCYWxhbmNlICYmIChcbiAgICAgICAgICAgIDxBbGVydCBjbGFzc05hbWU9XCJtdC0zIGJvcmRlci1yZWQtMjAwIGJnLXJlZC01MFwiPlxuICAgICAgICAgICAgICA8SW5mbyBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgPEFsZXJ0RGVzY3JpcHRpb24gY2xhc3NOYW1lPVwidGV4dC1yZWQtODAwXCI+XG4gICAgICAgICAgICAgICAgTW9zdCBwcmVtaXVtIGNoYXB0ZXJzIGNvc3QgMy0xMCBjcmVkaXRzLiBDb25zaWRlciBwdXJjaGFzaW5nIG1vcmUgdG8gY29udGludWUgcmVhZGluZy5cbiAgICAgICAgICAgICAgPC9BbGVydERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgPC9BbGVydD5cbiAgICAgICAgICApfVxuICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgPC9DYXJkPlxuXG4gICAgICB7LyogQ3JlZGl0IFB1cmNoYXNlIE1vZGFsICovfVxuICAgICAge3Nob3dQdXJjaGFzZU1vZGFsICYmIChcbiAgICAgICAgPENyZWRpdFB1cmNoYXNlTW9kYWxcbiAgICAgICAgICBpc09wZW49e3Nob3dQdXJjaGFzZU1vZGFsfVxuICAgICAgICAgIG9uQ2xvc2U9eygpID0+IHNldFNob3dQdXJjaGFzZU1vZGFsKGZhbHNlKX1cbiAgICAgICAgICBvblN1Y2Nlc3M9eygpID0+IHtcbiAgICAgICAgICAgIHJlZmV0Y2hCYWxhbmNlKClcbiAgICAgICAgICAgIHNldFNob3dQdXJjaGFzZU1vZGFsKGZhbHNlKVxuICAgICAgICAgIH19XG4gICAgICAgIC8+XG4gICAgICApfVxuICAgIDwvPlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU2Vzc2lvbiIsInVzZURpc3BhdGNoIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQnV0dG9uIiwiQmFkZ2UiLCJTa2VsZXRvbiIsIkFsZXJ0IiwiQWxlcnREZXNjcmlwdGlvbiIsIkNvaW5zIiwiUGx1cyIsIlRyZW5kaW5nRG93biIsIlRyZW5kaW5nVXAiLCJJbmZvIiwiQWxlcnRUcmlhbmdsZSIsImZvcm1hdENyZWRpdHMiLCJDcmVkaXRQdXJjaGFzZU1vZGFsIiwidXNlR2V0Q3JlZGl0QmFsYW5jZVF1ZXJ5IiwiQ3JlZGl0U3RhdHVzQmFyIiwiY2xhc3NOYW1lIiwic2hvd1JlY29tbWVuZGF0aW9ucyIsIm1pbmltdW1SZWNvbW1lbmRlZCIsImRhdGEiLCJzZXNzaW9uIiwiZGlzcGF0Y2giLCJiYWxhbmNlRGF0YSIsImlzTG9hZGluZyIsImlzTG9hZGluZ0JhbGFuY2UiLCJ1bmRlZmluZWQiLCJza2lwIiwidXNlciIsInBvbGxpbmdJbnRlcnZhbCIsImN1cnJlbnRCYWxhbmNlIiwiYmFsYW5jZSIsInJvbGUiLCJpc0xvd0JhbGFuY2UiLCJpc1ZlcnlMb3dCYWxhbmNlIiwiZGl2Iiwic3BhbiIsInZhcmlhbnQiLCJvbkNsaWNrIiwic2V0U2hvd1B1cmNoYXNlTW9kYWwiLCJzaXplIiwic2hvd1B1cmNoYXNlTW9kYWwiLCJpc09wZW4iLCJvbkNsb3NlIiwib25TdWNjZXNzIiwicmVmZXRjaEJhbGFuY2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/reader/credit-status-bar.tsx\n"));

/***/ })

});