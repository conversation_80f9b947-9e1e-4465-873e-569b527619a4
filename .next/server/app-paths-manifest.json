{"/api/credits/balance/route": "app/api/credits/balance/route.js", "/settings/page": "app/settings/page.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/profile/page": "app/profile/page.js", "/page": "app/page.js", "/auth/signin/page": "app/auth/signin/page.js", "/api/novels/route": "app/api/novels/route.js", "/browse/page": "app/browse/page.js", "/api/library/check/[novelId]/route": "app/api/library/check/[novelId]/route.js", "/novels/[id]/page": "app/novels/[id]/page.js", "/api/novels/[id]/route": "app/api/novels/[id]/route.js", "/api/chapters/[id]/pricing/route": "app/api/chapters/[id]/pricing/route.js", "/api/credits/packages/route": "app/api/credits/packages/route.js"}