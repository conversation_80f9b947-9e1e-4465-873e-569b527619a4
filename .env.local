# Database (SQLite for testing - temporary)
DATABASE_URL="file:./dev.db"
DIRECT_URL="file:./dev.db"

# Original Supabase (commented out)
# DATABASE_URL="*****************************************************************************/postgres"
# DIRECT_URL="*****************************************************************************/postgres"

# NextAuth
NEXTAUTH_SECRET="super-secret-jwt-token-with-at-least-32-characters-long"
NEXTAUTH_URL="http://localhost:3000"

# Google OAuth
GOOGLE_CLIENT_ID="352602036072-f8t5mnehif8a361et56b6lqh8v2dsnpl.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"

# Supabase Hosted
NEXT_PUBLIC_SUPABASE_URL="https://iiicswpgsxczxlitfxgh.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY=""
SUPABASE_SERVICE_ROLE_KEY=""

# Google Cloud Storage (using current gcloud credentials)
# GOOGLE_CLOUD_PROJECT_ID="gen-lang-client-0346143905"  # Auto-detected from gcloud
GOOGLE_CLOUD_BUCKET_NAME="gen-lang-client-0346143905-media"
# No need for key file or credentials - using gcloud auth

# App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_APP_NAME="Black Blog"

# Stripe Configuration
STRIPE_SECRET_KEY="sk_test_51RZ18cE7j6iXdCGyyVxUqneluI9gMbyJMmYFfnlxHmMxkUdiloK27VvjIKy366Ux0MFKLK2IGloUoFWB9xaEnmZO00LOM67NXn"
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_51RZ18cE7j6iXdCGyykJ9YwinC6IlOmaUNxcuMdDDWaNuzugT4uhBIbQ3dg1bxb5J8DYUVnepXotfEswjis0Z0knL00Tmsa0Jlj"
STRIPE_WEBHOOK_SECRET="whsec_43df8098fa02a73d7e5b259067dcc7a12f8f0eae7866b7a2089f0a75b3ec1b32"

# Stripe Price IDs (created via Stripe CLI)
STRIPE_PREMIUM_MONTHLY_PRICE_ID="price_1RigbuE7j6iXdCGyzREyXZ8S"
STRIPE_PREMIUM_YEARLY_PRICE_ID="price_1Rigc2E7j6iXdCGy6fknltrF"
STRIPE_PREMIUM_PLUS_MONTHLY_PRICE_ID="price_1RigcKE7j6iXdCGyJY05suM0"
STRIPE_PREMIUM_PLUS_YEARLY_PRICE_ID="price_1RigcTE7j6iXdCGyGs7A7Yqr"