# Email Notification System

This document describes the comprehensive email notification system for the credit top-up functionality.

## Overview

The email notification system provides automated and manual email communications for:
- Credit purchase confirmations
- Low balance warnings
- Purchase reminders for abandoned carts
- Weekly reading digests
- Administrative notifications

## Architecture

### Core Components

1. **EmailService** (`src/lib/email/email-service.ts`)
   - Multi-provider email service supporting SendGrid, Mailgun, AWS SES, Resend, and SMTP
   - Automatic provider selection based on environment variables
   - Email logging and statistics tracking
   - Template processing with variable substitution

2. **CreditPurchaseNotificationService** (`src/lib/notifications/credit-purchase-notifications.ts`)
   - Specialized service for credit-related email notifications
   - HTML and text email generation
   - Purchase confirmation and low balance warning emails

3. **EmailScheduler** (`src/lib/notifications/email-scheduler.ts`)
   - Automated email scheduling and batch processing
   - Low balance checks, purchase reminders, and digest emails
   - Configurable frequency and targeting

## Email Types

### 1. Purchase Confirmation Emails
- **Trigger**: Successful credit purchase
- **Content**: Purchase details, credits received, transaction ID
- **Template**: HTML with receipt-style formatting
- **Timing**: Immediate after successful payment

### 2. Low Balance Warning Emails
- **Trigger**: User balance below 20 credits
- **Content**: Current balance, recommended packages, top-up links
- **Frequency**: Maximum once per week per user
- **Template**: Warning-style with recommendations

### 3. Purchase Reminder Emails
- **Trigger**: Abandoned purchase (pending for 2+ hours)
- **Content**: Incomplete purchase details, completion link
- **Frequency**: Once per abandoned purchase
- **Template**: Reminder with urgency elements

### 4. Weekly Digest Emails
- **Trigger**: Weekly schedule for active users
- **Content**: Reading stats, credit usage, balance status
- **Frequency**: Weekly (configurable)
- **Template**: Summary-style with statistics

## Configuration

### Environment Variables

```bash
# Email Provider Configuration (choose one)
SENDGRID_API_KEY=your_sendgrid_key
MAILGUN_API_KEY=your_mailgun_key
MAILGUN_DOMAIN=your_mailgun_domain
AWS_SES_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_key
AWS_SECRET_ACCESS_KEY=your_aws_secret
RESEND_API_KEY=your_resend_key

# SMTP Configuration (alternative)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password
SMTP_SECURE=false

# General Email Settings
EMAIL_FROM="Black Blogs <<EMAIL>>"
CRON_SECRET=your_cron_secret_for_scheduled_jobs
```

### Database Schema

The system expects these optional database tables:

```sql
-- Email logging (optional)
CREATE TABLE email_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  to_email VARCHAR(255) NOT NULL,
  subject VARCHAR(500) NOT NULL,
  provider VARCHAR(50) NOT NULL,
  message_id VARCHAR(255),
  success BOOLEAN NOT NULL,
  error TEXT,
  sent_at TIMESTAMP DEFAULT NOW()
);

-- Email notifications tracking (optional)
CREATE TABLE email_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  type VARCHAR(100) NOT NULL,
  recipient VARCHAR(255) NOT NULL,
  subject VARCHAR(500) NOT NULL,
  content TEXT,
  status VARCHAR(50) NOT NULL,
  message_id VARCHAR(255),
  metadata JSONB,
  created_at TIMESTAMP DEFAULT NOW()
);

-- User email preferences
ALTER TABLE users ADD COLUMN email_notifications BOOLEAN DEFAULT true;
ALTER TABLE users ADD COLUMN low_balance_warnings BOOLEAN DEFAULT true;
ALTER TABLE users ADD COLUMN purchase_confirmations BOOLEAN DEFAULT true;
ALTER TABLE users ADD COLUMN weekly_digest BOOLEAN DEFAULT false;
ALTER TABLE users ADD COLUMN marketing_emails BOOLEAN DEFAULT false;
ALTER TABLE users ADD COLUMN purchase_reminders BOOLEAN DEFAULT true;
ALTER TABLE users ADD COLUMN last_low_balance_warning TIMESTAMP;
```

## API Endpoints

### User Email Preferences
- `GET /api/user/email-preferences` - Get user's email preferences
- `PUT /api/user/email-preferences` - Update user's email preferences
- `POST /api/user/email-preferences` - Unsubscribe from emails

### Admin Email Management
- `GET /api/admin/emails` - Get email statistics and logs
- `POST /api/admin/emails` - Send test emails or trigger jobs

### Scheduled Jobs
- `GET /api/cron/emails` - Run scheduled email jobs (for cron services)
- `POST /api/cron/emails` - Manual trigger for scheduled jobs

## Scheduled Jobs

### Setup with Vercel Cron

Add to `vercel.json`:

```json
{
  "crons": [
    {
      "path": "/api/cron/emails?job=low_balance",
      "schedule": "0 9 * * 1"
    },
    {
      "path": "/api/cron/emails?job=purchase_reminders",
      "schedule": "0 */6 * * *"
    },
    {
      "path": "/api/cron/emails?job=weekly_digest",
      "schedule": "0 10 * * 0"
    }
  ]
}
```

### Manual Scheduling

```bash
# Run low balance check
curl -X GET "https://yourapp.com/api/cron/emails?job=low_balance" \
  -H "Authorization: Bearer your_cron_secret"

# Run all scheduled jobs
curl -X GET "https://yourapp.com/api/cron/emails?job=all" \
  -H "Authorization: Bearer your_cron_secret"
```

## Usage Examples

### Sending Purchase Confirmation

```typescript
import { CreditPurchaseNotificationService } from '@/lib/notifications/credit-purchase-notifications'

await CreditPurchaseNotificationService.sendPurchaseConfirmation({
  purchaseId: 'purchase_123',
  userEmail: '<EMAIL>',
  userName: 'John Doe',
  packageName: 'Value Pack',
  credits: 100,
  bonusCredits: 20,
  totalCredits: 120,
  amount: 10.00,
  currency: 'usd',
  purchaseDate: new Date(),
  stripePaymentId: 'pi_123'
})
```

### Sending Low Balance Warning

```typescript
await CreditPurchaseNotificationService.sendLowBalanceWarning({
  userEmail: '<EMAIL>',
  userName: 'John Doe',
  currentBalance: 5,
  recommendedPackage: {
    name: 'Starter Pack',
    credits: 50,
    price: 5.00
  }
})
```

### Running Scheduled Jobs

```typescript
import { EmailScheduler } from '@/lib/notifications/email-scheduler'

// Run weekly low balance check
await EmailScheduler.scheduleWeeklyLowBalanceCheck()

// Send purchase reminders
await EmailScheduler.schedulePurchaseReminders()

// Send weekly digest
await EmailScheduler.sendWeeklyDigest()

// Run all jobs
await EmailScheduler.runScheduledJobs()
```

## Testing

### Test Email Sending

```bash
# Send test purchase confirmation
curl -X POST "https://yourapp.com/api/admin/emails" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer admin_token" \
  -d '{
    "action": "send_test_email",
    "to": "<EMAIL>",
    "type": "purchase_confirmation",
    "data": {
      "userName": "Test User",
      "packageName": "Test Package",
      "credits": 100,
      "bonusCredits": 10,
      "amount": 10.00
    }
  }'
```

### Trigger Scheduled Jobs

```bash
# Trigger low balance check
curl -X POST "https://yourapp.com/api/admin/emails" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer admin_token" \
  -d '{
    "action": "trigger_scheduled_job",
    "job": "low_balance_check"
  }'
```

## Monitoring and Analytics

### Email Statistics

The system tracks:
- Total emails sent
- Success/failure rates
- Provider performance
- Email types and frequency
- User engagement metrics

### Logs and Debugging

- All emails are logged to the database (if table exists)
- Failed emails include error details
- Console logging for development environments
- Email statistics available via admin API

## Best Practices

1. **Provider Redundancy**: Configure multiple email providers for failover
2. **Rate Limiting**: Respect provider rate limits and implement queuing
3. **User Preferences**: Always respect user email preferences
4. **Unsubscribe Links**: Include unsubscribe links in all marketing emails
5. **Testing**: Use test mode for development and staging environments
6. **Monitoring**: Set up alerts for email delivery failures
7. **Compliance**: Follow CAN-SPAM and GDPR requirements

## Troubleshooting

### Common Issues

1. **Emails not sending**: Check provider configuration and API keys
2. **High bounce rates**: Verify email addresses and sender reputation
3. **Spam folder delivery**: Configure SPF, DKIM, and DMARC records
4. **Rate limiting**: Implement exponential backoff and queuing
5. **Template errors**: Validate template variables and HTML structure

### Debug Mode

Set `NODE_ENV=development` to use console provider for testing without sending real emails.

## Security Considerations

- Store API keys securely in environment variables
- Use HTTPS for all email-related endpoints
- Implement rate limiting on email endpoints
- Validate and sanitize all email content
- Use secure unsubscribe tokens
- Log email activities for audit trails
