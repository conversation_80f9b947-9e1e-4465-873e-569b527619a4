#!/usr/bin/env node

/**
 * Credit System Test Runner
 * 
 * This script runs comprehensive tests for the credit top-up system including:
 * - Unit tests for credit transactions
 * - Integration tests for payment flows
 * - API endpoint tests
 * - Database consistency checks
 * - Stripe test mode validation
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function runCommand(command, description) {
  log(`\n${description}...`, 'cyan')
  try {
    const output = execSync(command, { 
      encoding: 'utf8', 
      stdio: 'pipe',
      maxBuffer: 1024 * 1024 * 10 // 10MB buffer
    })
    log(`✅ ${description} completed successfully`, 'green')
    return { success: true, output }
  } catch (error) {
    log(`❌ ${description} failed:`, 'red')
    log(error.stdout || error.message, 'red')
    return { success: false, error: error.stdout || error.message }
  }
}

function checkPrerequisites() {
  log('🔍 Checking prerequisites...', 'blue')
  
  // Check if test database is configured
  if (!process.env.TEST_DATABASE_URL && !process.env.DATABASE_URL) {
    log('❌ No test database configured. Please set TEST_DATABASE_URL or DATABASE_URL', 'red')
    return false
  }

  // Check if Stripe test keys are configured
  if (!process.env.STRIPE_SECRET_KEY || !process.env.STRIPE_SECRET_KEY.includes('sk_test_')) {
    log('⚠️  Warning: Stripe test key not configured. Some tests may fail.', 'yellow')
  }

  // Check if required files exist
  const requiredFiles = [
    'src/lib/testing/credit-test-utils.ts',
    'src/__tests__/credit-system.test.ts',
    'src/__tests__/api/credits.test.ts',
    'jest.config.js'
  ]

  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      log(`❌ Required test file missing: ${file}`, 'red')
      return false
    }
  }

  log('✅ All prerequisites met', 'green')
  return true
}

function runTestSuite() {
  log('\n🧪 Running Credit System Test Suite', 'bright')
  log('=' .repeat(50), 'blue')

  const testResults = []

  // 1. Run unit tests
  const unitTests = runCommand(
    'npm run test -- --testPathPattern=credit-system.test.ts --verbose',
    'Running unit tests for credit transactions'
  )
  testResults.push({ name: 'Unit Tests', ...unitTests })

  // 2. Run API tests
  const apiTests = runCommand(
    'npm run test -- --testPathPattern=api/credits.test.ts --verbose',
    'Running API endpoint tests'
  )
  testResults.push({ name: 'API Tests', ...apiTests })

  // 3. Run integration tests with coverage
  const integrationTests = runCommand(
    'npm run test -- --testPathPattern=credit --coverage --coverageReporters=text',
    'Running integration tests with coverage'
  )
  testResults.push({ name: 'Integration Tests', ...integrationTests })

  // 4. Run database consistency checks
  const dbTests = runCommand(
    'npm run test -- --testPathPattern=credit-system.test.ts --testNamePattern="validate.*consistency" --verbose',
    'Running database consistency checks'
  )
  testResults.push({ name: 'Database Consistency', ...dbTests })

  return testResults
}

function runStripeTests() {
  log('\n💳 Running Stripe Integration Tests', 'bright')
  log('=' .repeat(50), 'blue')

  if (!process.env.STRIPE_SECRET_KEY || !process.env.STRIPE_SECRET_KEY.includes('sk_test_')) {
    log('⚠️  Skipping Stripe tests - no test key configured', 'yellow')
    return { skipped: true }
  }

  // Run Stripe-specific tests
  const stripeTests = runCommand(
    'npm run test -- --testPathPattern=credit --testNamePattern="Payment Integration" --verbose',
    'Running Stripe payment integration tests'
  )

  return stripeTests
}

function runPerformanceTests() {
  log('\n⚡ Running Performance Tests', 'bright')
  log('=' .repeat(50), 'blue')

  // Run concurrent transaction tests
  const concurrencyTests = runCommand(
    'npm run test -- --testPathPattern=credit-system.test.ts --testNamePattern="concurrent" --verbose',
    'Running concurrent transaction tests'
  )

  return concurrencyTests
}

function generateTestReport(results) {
  log('\n📊 Test Report', 'bright')
  log('=' .repeat(50), 'blue')

  let totalTests = 0
  let passedTests = 0
  let failedTests = 0

  results.forEach(result => {
    if (result.skipped) {
      log(`⏭️  ${result.name}: Skipped`, 'yellow')
    } else if (result.success) {
      log(`✅ ${result.name}: Passed`, 'green')
      passedTests++
    } else {
      log(`❌ ${result.name}: Failed`, 'red')
      failedTests++
    }
    totalTests++
  })

  log(`\nSummary:`, 'bright')
  log(`Total test suites: ${totalTests}`)
  log(`Passed: ${passedTests}`, 'green')
  log(`Failed: ${failedTests}`, failedTests > 0 ? 'red' : 'reset')
  log(`Success rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`, 
       failedTests === 0 ? 'green' : 'yellow')

  // Generate detailed report file
  const reportPath = path.join(process.cwd(), 'test-report.json')
  fs.writeFileSync(reportPath, JSON.stringify({
    timestamp: new Date().toISOString(),
    summary: {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      successRate: (passedTests / totalTests) * 100
    },
    results
  }, null, 2))

  log(`\n📄 Detailed report saved to: ${reportPath}`, 'cyan')

  return failedTests === 0
}

function main() {
  log('🚀 Credit System Test Runner', 'bright')
  log('Testing comprehensive credit top-up system functionality\n', 'cyan')

  // Check prerequisites
  if (!checkPrerequisites()) {
    process.exit(1)
  }

  const allResults = []

  // Run main test suite
  const testResults = runTestSuite()
  allResults.push(...testResults)

  // Run Stripe tests
  const stripeResult = runStripeTests()
  if (stripeResult.skipped) {
    allResults.push({ name: 'Stripe Integration', skipped: true })
  } else {
    allResults.push({ name: 'Stripe Integration', ...stripeResult })
  }

  // Run performance tests
  const performanceResult = runPerformanceTests()
  allResults.push({ name: 'Performance Tests', ...performanceResult })

  // Generate report
  const allTestsPassed = generateTestReport(allResults)

  if (allTestsPassed) {
    log('\n🎉 All tests passed! Credit system is ready for production.', 'green')
    process.exit(0)
  } else {
    log('\n💥 Some tests failed. Please review the errors above.', 'red')
    process.exit(1)
  }
}

// Run if called directly
if (require.main === module) {
  main()
}

module.exports = {
  runTestSuite,
  runStripeTests,
  runPerformanceTests,
  generateTestReport
}
